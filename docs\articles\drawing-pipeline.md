# Understanding the Drawing Pipeline

DrawnUI's drawing pipeline transforms your logical UI controls into pixel-perfect drawings on a Skia canvas. 
This article explains how the pipeline works, from initial layout calculations to final rendering.
DrawnUI in it's self is not designed for "unaware" usage, one needs to understand how it works in order to achive the best performance and results.  
At the same time dedicated "easy-to-use" controls can be created as wrappers around the base controls, that would automaticaly and some are provided for common use-cases, so you can start using DrawnUI without deep understanding of it's internals. 

## Overview

The DrawnUI drawing pipeline consists of several key stages:

1. **Executing pre-draw actions** - including gestures processing, animations, etc.
2. **Measuring and arranging** - measure and arrange self and children if layout was invalidated
3. **Drawing and caching**   - painting and caching for future fast re-draws of recorded SKPicture or rasterized SKImage
4. **Executing post-draw actions** - like post-animations for overlays, etc.

## Pipeline Flow

### 1. Invalidation Stage

The pipeline begins when a control needs to be redrawn. This can happen due to:

- Property changes (color, size, text, etc.)
- Layout changes (adding/removing children)
- Animation updates
- User interactions

```csharp
// Most commonly used invalidation methods
control.Update();            // Mark for redraw (Update), invalidates cache
control.Repaint();           // Mark parent for redraw (Update), to repaint without destroying cache, at new positions, transformed etc
control.Invalidate();        // Invalidate and (maybe, depending on this control  logic) update
control.InvalidateMeasure(); // Just recalculate size and layout and update
control.Parent?.Invalidate() // When the above doesn't work if parent refuses to invalidate due to its internal logic
```

### 2. Measure Stage

During the measure stage, controls calculate their desired size based on available space and content requirements.

```csharp
public virtual ScaledSize Measure(float widthConstraint, float heightConstraint, float scale)
{
    // Create measure request with constraints
    var request = CreateMeasureRequest(widthConstraint, heightConstraint, scale);
    
    // Measure content and return desired size
    return MeasureLayout(request, false);
}
```

**Measure Process:**
1. **Constraints Calculation** - Determine available space considering margins and padding
2. **Content Measurement** - Measure child controls based on layout type
3. **Size Request** - Calculate final desired size

**Layout Types:**
- `Absolute` - Children positioned at specific coordinates
- `Column/Row` - Stack children vertically or horizontally  
- `Grid` - Arrange children in rows and columns
- `Wrap` - Flow children with wrapping

### 3. Arrange Stage

The arrange stage positions controls within their allocated space and calculates final drawing rectangles.

```csharp
public virtual void Arrange(SKRect destination, float widthRequest, float heightRequest, float scale)
{
    // Pre-arrange validation
    if (!PreArrange(destination, widthRequest, heightRequest, scale))
        return;
        
    // Calculate final layout
    var layout = CalculateLayout(arrangingFor, widthRequest, heightRequest, scale);
    
    // Set drawing rectangle
    DrawingRect = layout;
    
    // Post-arrange processing
    PostArrange(destination, widthRequest, heightRequest, scale);
}
```

**Arrange Process:**
1. **Pre-Arrange** - Validate and prepare for layout
2. **Layout Calculation** - Determine final position and size
3. **Drawing Rectangle** - Set the area where control will be drawn
4. **Post-Arrange** - Cache layout information and handle changes

### 4. Paint Stage

The paint stage renders the actual visual content to the Skia canvas.

```csharp
protected virtual void Paint(DrawingContext ctx)
{
    // Paint background
    PaintTintBackground(ctx.Context.Canvas, ctx.Destination);
    
    // Execute custom paint operations
    foreach (var action in ExecuteOnPaint.Values)
    {
        action?.Invoke(this, ctx);
    }
}
```

**Drawing Context:**
- `SKCanvas` - The Skia drawing surface
- `SKRect Destination` - Where to draw in pixels
- `float Scale` - Pixel density scaling factor
- `object Parameters` - Optional custom parameters

### 5. Caching System

DrawnUI uses sophisticated caching to optimize rendering performance through render objects.

#### Cache Types

```csharp
public enum SkiaCacheType
{
    None,                    // No caching, direct drawing
    Operations,              // Cache drawing operations (SKPicture)
    OperationsFull,          // Cache operations ignoring clipping
    Image,                   // Cache as bitmap (SKBitmap)
    ImageComposite,          // Advanced bitmap caching
    ImageDoubleBuffered,     // Background thread caching
    GPU                      // Hardware-accelerated caching
}
```

#### Render Object Pipeline

```csharp
public virtual bool DrawUsingRenderObject(DrawingContext context, 
    float widthRequest, float heightRequest)
{
    // 1. Arrange the control
    Arrange(context.Destination, widthRequest, heightRequest, context.Scale);
    
    // 2. Check if we can use cached render object
    if (RenderObject != null && CheckCachedObjectValid(RenderObject))
    {
        DrawRenderObjectInternal(context, RenderObject);
        return true;
    }
    
    // 3. Create new render object if needed
    var cache = CreateRenderingObject(context, recordArea, oldObject, UsingCacheType,
        (ctx) => { PaintWithEffects(ctx); });
        
    // 4. Draw using the render object
    DrawRenderObjectInternal(context, cache);
    
    return true;
}
```

#### Cache Validation

Render objects are invalidated when:
- Control size changes
- Visual properties change (colors, effects, etc.)
- Child controls are modified
- Animation state updates

### 6. Gesture Processing

The gesture system processes user input through a hierarchical hit-testing approach.

```csharp
public ISkiaGestureListener OnSkiaGestureEvent(SkiaGesturesParameters args, 
    GestureEventProcessingInfo apply)
{
    // Process gesture if control can handle input
    if (CanDraw && this is ISkiaGestureListener listener)
    {
        var result = ProcessGestures(args, apply);
        return result; // Return consumer or null
    }
    return null;
}
```

**Gesture Flow:**
1. **Hit Testing** - Determine which controls are under the touch point
2. **Gesture Recognition** - Identify gesture type (tap, drag, pinch, etc.)
3. **Event Propagation** - Pass gestures through control hierarchy
4. **Consumption** - Allow controls to consume or pass through gestures

## Performance Optimizations

### 1. Render Object Caching

- **Operations Caching** - Best for static content like text and shapes
- **Image Caching** - Ideal for complex graphics with effects
- **Double Buffering** - Renders on background thread for smooth animations

### 2. Layout Optimization

- **Layout Dirty Tracking** - Only re-layout when necessary
- **Measure Caching** - Reuse previous measurements when possible
- **Viewport Limiting** - Only process visible content

### 3. Drawing Optimizations

- **Clipping** - Skip drawing outside visible areas
- **Transform Caching** - Reuse transformation matrices
- **Effect Batching** - Group similar drawing operations

## Common Patterns

### Custom Control Drawing

```csharp
public class MyCustomControl : SkiaControl
{
    protected override void Paint(DrawingContext ctx)
    {
        base.Paint(ctx); // Paint background
        
        var canvas = ctx.Context.Canvas;
        var rect = ctx.Destination;
        
        // Custom drawing code here
        using var paint = new SKPaint
        {
            Color = SKColors.Blue,
            IsAntialias = true
        };
        
        canvas.DrawCircle(rect.MidX, rect.MidY, 
            Math.Min(rect.Width, rect.Height) / 2, paint);
    }
}
```

### Layout Container

```csharp
public class MyLayout : SkiaLayout
{
    protected override ScaledSize MeasureAbsolute(SKRect rectForChildrenPixels, float scale)
    {
        // Measure all children
        foreach (var child in Views)
        {
            var childSize = MeasureChild(child, 
                rectForChildrenPixels.Width, 
                rectForChildrenPixels.Height, scale);
        }
        
        // Return total size needed
        return ScaledSize.FromPixels(totalWidth, totalHeight, scale);
    }
    
    protected override void ArrangeChildren(SKRect rectForChildrenPixels, float scale)
    {
        // Position each child
        foreach (var child in Views)
        {
            var childRect = CalculateChildPosition(child, rectForChildrenPixels);
            child.Arrange(childRect, child.SizeRequest.Width, child.SizeRequest.Height, scale);
        }
    }
}
```

## Debugging the Pipeline

### Performance Monitoring

```csharp
// Enable performance tracking
Super.EnableRenderingStats = true;

// Monitor frame rates
var fps = canvasView.FPS;
var frameTime = canvasView.FrameTime;
```

### Visual Debugging

```csharp
// Show control boundaries
control.DebugShowBounds = true;

// Highlight invalidated areas
Super.ShowInvalidatedAreas = true;
```

## Best Practices

1. **Use Appropriate Caching** - Choose the right `SkiaCacheType` for your content
2. **Minimize Invalidations** - Batch property changes to reduce redraws
3. **Optimize Custom Paint** - Keep `Paint()` methods lightweight
4. **Profile Performance** - Use built-in performance monitoring tools
5. **Handle Gestures Efficiently** - Return appropriate consumers to optimize hit-testing

## Conclusion

Understanding DrawnUI's drawing pipeline helps you build efficient, responsive applications. The pipeline's sophisticated caching and optimization systems ensure smooth performance while providing the flexibility to create custom, pixel-perfect user interfaces.

The key to success with DrawnUI is working with the pipeline rather than against it - use the provided caching mechanisms, minimize unnecessary invalidations, and leverage the hierarchical layout system for optimal performance.
