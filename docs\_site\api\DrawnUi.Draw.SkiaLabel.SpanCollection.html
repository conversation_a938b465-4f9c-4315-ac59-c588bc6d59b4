<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
      <title>Class SkiaLabel.SpanCollection | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width">
      <meta name="title" content="Class SkiaLabel.SpanCollection | DrawnUi Documentation ">
    
    
      <link rel="shortcut icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../styles/docfx.vendor.min.css">
      <link rel="stylesheet" href="../styles/docfx.css">
      <link rel="stylesheet" href="../styles/main.css">
      <meta property="docfx:navrel" content="../toc.html">
      <meta property="docfx:tocrel" content="toc.html">
    
    <meta property="docfx:rel" content="../">
    
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first=First data-prev=Previous data-next=Next data-last=Last></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">

        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="DrawnUi.Draw.SkiaLabel.SpanCollection">



  <h1 id="DrawnUi_Draw_SkiaLabel_SpanCollection" data-uid="DrawnUi.Draw.SkiaLabel.SpanCollection" class="text-break">Class SkiaLabel.SpanCollection</h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></div>
    <div class="level1"><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1">Collection</a>&lt;<a class="xref" href="DrawnUi.Draw.TextSpan.html">TextSpan</a>&gt;</div>
    <div class="level2"><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1">ObservableCollection</a>&lt;<a class="xref" href="DrawnUi.Draw.TextSpan.html">TextSpan</a>&gt;</div>
    <div class="level3"><span class="xref">ObservableRangeCollection</span>&lt;<a class="xref" href="DrawnUi.Draw.TextSpan.html">TextSpan</a>&gt;</div>
    <div class="level4"><span class="xref">SkiaLabel.SpanCollection</span></div>
  </div>
  <div class="implements">
    <h5>Implements</h5>
    <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.ilist-1">IList</a>&lt;<a class="xref" href="DrawnUi.Draw.TextSpan.html">TextSpan</a>&gt;</div>
    <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.icollection-1">ICollection</a>&lt;<a class="xref" href="DrawnUi.Draw.TextSpan.html">TextSpan</a>&gt;</div>
    <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.ireadonlylist-1">IReadOnlyList</a>&lt;<a class="xref" href="DrawnUi.Draw.TextSpan.html">TextSpan</a>&gt;</div>
    <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.ireadonlycollection-1">IReadOnlyCollection</a>&lt;<a class="xref" href="DrawnUi.Draw.TextSpan.html">TextSpan</a>&gt;</div>
    <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1">IEnumerable</a>&lt;<a class="xref" href="DrawnUi.Draw.TextSpan.html">TextSpan</a>&gt;</div>
    <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.ilist">IList</a></div>
    <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.icollection">ICollection</a></div>
    <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.ienumerable">IEnumerable</a></div>
    <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.specialized.inotifycollectionchanged">INotifyCollectionChanged</a></div>
    <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.componentmodel.inotifypropertychanged">INotifyPropertyChanged</a></div>
  </div>
  <div class="inheritedMembers">
    <h5>Inherited Members</h5>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1">ObservableRangeCollection&lt;TextSpan&gt;.AddRange(IEnumerable&lt;TextSpan&gt;, NotifyCollectionChangedAction)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1">ObservableRangeCollection&lt;TextSpan&gt;.RemoveRange(IEnumerable&lt;TextSpan&gt;, NotifyCollectionChangedAction)</a>
    </div>
    <div>
      <span class="xref">ObservableRangeCollection&lt;TextSpan&gt;.Replace(TextSpan)</span>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1">ObservableRangeCollection&lt;TextSpan&gt;.ReplaceRange(IEnumerable&lt;TextSpan&gt;)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.blockreentrancy">ObservableCollection&lt;TextSpan&gt;.BlockReentrancy()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.checkreentrancy">ObservableCollection&lt;TextSpan&gt;.CheckReentrancy()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.move">ObservableCollection&lt;TextSpan&gt;.Move(int, int)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.moveitem">ObservableCollection&lt;TextSpan&gt;.MoveItem(int, int)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.oncollectionchanged">ObservableCollection&lt;TextSpan&gt;.OnCollectionChanged(NotifyCollectionChangedEventArgs)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.onpropertychanged">ObservableCollection&lt;TextSpan&gt;.OnPropertyChanged(PropertyChangedEventArgs)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.removeitem">ObservableCollection&lt;TextSpan&gt;.RemoveItem(int)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.collectionchanged">ObservableCollection&lt;TextSpan&gt;.CollectionChanged</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.propertychanged">ObservableCollection&lt;TextSpan&gt;.PropertyChanged</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.add">Collection&lt;TextSpan&gt;.Add(TextSpan)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.clear">Collection&lt;TextSpan&gt;.Clear()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.contains">Collection&lt;TextSpan&gt;.Contains(TextSpan)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.copyto">Collection&lt;TextSpan&gt;.CopyTo(TextSpan[], int)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.getenumerator">Collection&lt;TextSpan&gt;.GetEnumerator()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.indexof">Collection&lt;TextSpan&gt;.IndexOf(TextSpan)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.insert">Collection&lt;TextSpan&gt;.Insert(int, TextSpan)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.remove">Collection&lt;TextSpan&gt;.Remove(TextSpan)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.removeat">Collection&lt;TextSpan&gt;.RemoveAt(int)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.count">Collection&lt;TextSpan&gt;.Count</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">Collection&lt;TextSpan&gt;.this[int]</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.collection-1.items">Collection&lt;TextSpan&gt;.Items</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)">object.Equals(object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)">object.Equals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gethashcode">object.GetHashCode()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gettype">object.GetType()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone">object.MemberwiseClone()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.referenceequals">object.ReferenceEquals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.tostring">object.ToString()</a>
    </div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="DrawnUi.html">DrawnUi</a>.<a class="xref" href="DrawnUi.Draw.html">Draw</a></h6>
  <h6><strong>Assembly</strong>: DrawnUi.Maui.dll</h6>
  <h5 id="DrawnUi_Draw_SkiaLabel_SpanCollection_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public class SkiaLabel.SpanCollection : ObservableRangeCollection&lt;TextSpan&gt;, IList&lt;TextSpan&gt;, ICollection&lt;TextSpan&gt;, IReadOnlyList&lt;TextSpan&gt;, IReadOnlyCollection&lt;TextSpan&gt;, IEnumerable&lt;TextSpan&gt;, IList, ICollection, IEnumerable, INotifyCollectionChanged, INotifyPropertyChanged</code></pre>
  </div>
  <h3 id="methods">Methods
</h3>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_SkiaLabel_SpanCollection_ClearItems.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.SkiaLabel.SpanCollection.ClearItems%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Text/SkiaLabel.cs/#L286">View Source</a>
  </span>
  <a id="DrawnUi_Draw_SkiaLabel_SpanCollection_ClearItems_" data-uid="DrawnUi.Draw.SkiaLabel.SpanCollection.ClearItems*"></a>
  <h4 id="DrawnUi_Draw_SkiaLabel_SpanCollection_ClearItems" data-uid="DrawnUi.Draw.SkiaLabel.SpanCollection.ClearItems">ClearItems()</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.SkiaLabel.SpanCollection.yml" sourcestartlinenumber="1">Removes all items from the collection.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected override void ClearItems()</code></pre>
  </div>
  <h5 class="overrides">Overrides</h5>
  <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.clearitems">ObservableCollection&lt;TextSpan&gt;.ClearItems()</a></div>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_SkiaLabel_SpanCollection_InsertItem_System_Int32_DrawnUi_Draw_TextSpan_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.SkiaLabel.SpanCollection.InsertItem(System.Int32%2CDrawnUi.Draw.TextSpan)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Text/SkiaLabel.cs/#L280">View Source</a>
  </span>
  <a id="DrawnUi_Draw_SkiaLabel_SpanCollection_InsertItem_" data-uid="DrawnUi.Draw.SkiaLabel.SpanCollection.InsertItem*"></a>
  <h4 id="DrawnUi_Draw_SkiaLabel_SpanCollection_InsertItem_System_Int32_DrawnUi_Draw_TextSpan_" data-uid="DrawnUi.Draw.SkiaLabel.SpanCollection.InsertItem(System.Int32,DrawnUi.Draw.TextSpan)">InsertItem(int, TextSpan)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.SkiaLabel.SpanCollection.yml" sourcestartlinenumber="1">Inserts an item into the collection at the specified index.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected override void InsertItem(int index, TextSpan item)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></td>
        <td><span class="parametername">index</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.SkiaLabel.SpanCollection.yml" sourcestartlinenumber="1">The zero-based index at which <code class="paramref">item</code> should be inserted.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="DrawnUi.Draw.TextSpan.html">TextSpan</a></td>
        <td><span class="parametername">item</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.SkiaLabel.SpanCollection.yml" sourcestartlinenumber="1">The object to insert.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="overrides">Overrides</h5>
  <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.insertitem">ObservableCollection&lt;TextSpan&gt;.InsertItem(int, TextSpan)</a></div>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_SkiaLabel_SpanCollection_SetItem_System_Int32_DrawnUi_Draw_TextSpan_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.SkiaLabel.SpanCollection.SetItem(System.Int32%2CDrawnUi.Draw.TextSpan)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Text/SkiaLabel.cs/#L283">View Source</a>
  </span>
  <a id="DrawnUi_Draw_SkiaLabel_SpanCollection_SetItem_" data-uid="DrawnUi.Draw.SkiaLabel.SpanCollection.SetItem*"></a>
  <h4 id="DrawnUi_Draw_SkiaLabel_SpanCollection_SetItem_System_Int32_DrawnUi_Draw_TextSpan_" data-uid="DrawnUi.Draw.SkiaLabel.SpanCollection.SetItem(System.Int32,DrawnUi.Draw.TextSpan)">SetItem(int, TextSpan)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.SkiaLabel.SpanCollection.yml" sourcestartlinenumber="1">Replaces the element at the specified index.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected override void SetItem(int index, TextSpan item)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></td>
        <td><span class="parametername">index</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.SkiaLabel.SpanCollection.yml" sourcestartlinenumber="1">The zero-based index of the element to replace.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="DrawnUi.Draw.TextSpan.html">TextSpan</a></td>
        <td><span class="parametername">item</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.SkiaLabel.SpanCollection.yml" sourcestartlinenumber="1">The new value for the element at the specified index.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="overrides">Overrides</h5>
  <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.objectmodel.observablecollection-1.setitem">ObservableCollection&lt;TextSpan&gt;.SetItem(int, TextSpan)</a></div>
  <h3 id="implements">Implements</h3>
  <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.ilist-1">IList&lt;T&gt;</a>
  </div>
  <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.icollection-1">ICollection&lt;T&gt;</a>
  </div>
  <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.ireadonlylist-1">IReadOnlyList&lt;T&gt;</a>
  </div>
  <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.ireadonlycollection-1">IReadOnlyCollection&lt;T&gt;</a>
  </div>
  <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1">IEnumerable&lt;T&gt;</a>
  </div>
  <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.ilist">IList</a>
  </div>
  <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.icollection">ICollection</a>
  </div>
  <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.ienumerable">IEnumerable</a>
  </div>
  <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.specialized.inotifycollectionchanged">INotifyCollectionChanged</a>
  </div>
  <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.componentmodel.inotifypropertychanged">INotifyPropertyChanged</a>
  </div>
  <h3 id="extensionmethods">Extension Methods</h3>
  <div>
      <a class="xref" href="DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_">InternalExtensions.FromPlatform(object)</a>
  </div>

</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                  <li>
                    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_SkiaLabel_SpanCollection.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.SkiaLabel.SpanCollection%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A" class="contribution-link">Edit this page</a>
                  </li>
                  <li>
                    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Text/SkiaLabel.cs/#L278" class="contribution-link">View Source</a>
                  </li>
                </ul>
              </div>
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In this article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      
      <span>Generated by <strong>DocFX</strong></span>
          </div>
        </div>
      </footer>
    </div>

    <script type="text/javascript" src="../styles/docfx.vendor.min.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
