<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
      <title>Class Sk3dView | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width">
      <meta name="title" content="Class Sk3dView | DrawnUi Documentation ">
    
    <meta name="description" content="Custom implementation of Android&#39;s Camera 3D helper for SkiaSharp">
      <link rel="shortcut icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../styles/docfx.vendor.min.css">
      <link rel="stylesheet" href="../styles/docfx.css">
      <link rel="stylesheet" href="../styles/main.css">
      <meta property="docfx:navrel" content="../toc.html">
      <meta property="docfx:tocrel" content="toc.html">
    
    <meta property="docfx:rel" content="../">
    
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first=First data-prev=Previous data-next=Next data-last=Last></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">

        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="DrawnUi.Draw.Sk3dView">



  <h1 id="DrawnUi_Draw_Sk3dView" data-uid="DrawnUi.Draw.Sk3dView" class="text-break">Class Sk3dView</h1>
  <div class="markdown level0 summary"><p sourcefile="api/DrawnUi.Draw.Sk3dView.yml" sourcestartlinenumber="1">Custom implementation of Android's Camera 3D helper for SkiaSharp</p>
</div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></div>
    <div class="level1"><span class="xref">Sk3dView</span></div>
  </div>
  <div class="inheritedMembers">
    <h5>Inherited Members</h5>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)">object.Equals(object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)">object.Equals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gethashcode">object.GetHashCode()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gettype">object.GetType()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone">object.MemberwiseClone()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.referenceequals">object.ReferenceEquals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.tostring">object.ToString()</a>
    </div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="DrawnUi.html">DrawnUi</a>.<a class="xref" href="DrawnUi.Draw.html">Draw</a></h6>
  <h6><strong>Assembly</strong>: DrawnUi.Maui.dll</h6>
  <h5 id="DrawnUi_Draw_Sk3dView_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public class Sk3dView</code></pre>
  </div>
  <h3 id="constructors">Constructors
</h3>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_Sk3dView__ctor.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.Sk3dView.%23ctor%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Internals/Helpers/3D/Sk3dView.cs/#L20">View Source</a>
  </span>
  <a id="DrawnUi_Draw_Sk3dView__ctor_" data-uid="DrawnUi.Draw.Sk3dView.#ctor*"></a>
  <h4 id="DrawnUi_Draw_Sk3dView__ctor" data-uid="DrawnUi.Draw.Sk3dView.#ctor">Sk3dView()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Sk3dView()</code></pre>
  </div>
  <h3 id="fields">Fields
</h3>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_Sk3dView_CameraDistance.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.Sk3dView.CameraDistance%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Internals/Helpers/3D/Sk3dView.cs/#L34">View Source</a>
  </span>
  <h4 id="DrawnUi_Draw_Sk3dView_CameraDistance" data-uid="DrawnUi.Draw.Sk3dView.CameraDistance">CameraDistance</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.Sk3dView.yml" sourcestartlinenumber="1">3D camera distance (8 inches in pixels, similar to Android implementation)</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float CameraDistance</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_Sk3dView_Invalidated.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.Sk3dView.Invalidated%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Internals/Helpers/3D/Sk3dView.cs/#L25">View Source</a>
  </span>
  <h4 id="DrawnUi_Draw_Sk3dView_Invalidated" data-uid="DrawnUi.Draw.Sk3dView.Invalidated">Invalidated</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected bool Invalidated</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="properties">Properties
</h3>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_Sk3dView_Matrix.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.Sk3dView.Matrix%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Internals/Helpers/3D/Sk3dView.cs/#L179">View Source</a>
  </span>
  <a id="DrawnUi_Draw_Sk3dView_Matrix_" data-uid="DrawnUi.Draw.Sk3dView.Matrix*"></a>
  <h4 id="DrawnUi_Draw_Sk3dView_Matrix" data-uid="DrawnUi.Draw.Sk3dView.Matrix">Matrix</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.Sk3dView.yml" sourcestartlinenumber="1">Gets the current transformation matrix</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SKMatrix Matrix { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skmatrix">SKMatrix</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
</h3>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_Sk3dView_ApplyToCanvas_SkiaSharp_SKCanvas_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.Sk3dView.ApplyToCanvas(SkiaSharp.SKCanvas)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Internals/Helpers/3D/Sk3dView.cs/#L78">View Source</a>
  </span>
  <a id="DrawnUi_Draw_Sk3dView_ApplyToCanvas_" data-uid="DrawnUi.Draw.Sk3dView.ApplyToCanvas*"></a>
  <h4 id="DrawnUi_Draw_Sk3dView_ApplyToCanvas_SkiaSharp_SKCanvas_" data-uid="DrawnUi.Draw.Sk3dView.ApplyToCanvas(SkiaSharp.SKCanvas)">ApplyToCanvas(SKCanvas)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.Sk3dView.yml" sourcestartlinenumber="1">Applies the current 3D transformation to the canvas</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void ApplyToCanvas(SKCanvas canvas)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skcanvas">SKCanvas</a></td>
        <td><span class="parametername">canvas</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_Sk3dView_OnReset.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.Sk3dView.OnReset%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Internals/Helpers/3D/Sk3dView.cs/#L107">View Source</a>
  </span>
  <a id="DrawnUi_Draw_Sk3dView_OnReset_" data-uid="DrawnUi.Draw.Sk3dView.OnReset*"></a>
  <h4 id="DrawnUi_Draw_Sk3dView_OnReset" data-uid="DrawnUi.Draw.Sk3dView.OnReset">OnReset()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected virtual void OnReset()</code></pre>
  </div>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_Sk3dView_OnRestore.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.Sk3dView.OnRestore%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Internals/Helpers/3D/Sk3dView.cs/#L109">View Source</a>
  </span>
  <a id="DrawnUi_Draw_Sk3dView_OnRestore_" data-uid="DrawnUi.Draw.Sk3dView.OnRestore*"></a>
  <h4 id="DrawnUi_Draw_Sk3dView_OnRestore" data-uid="DrawnUi.Draw.Sk3dView.OnRestore">OnRestore()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected virtual void OnRestore()</code></pre>
  </div>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_Sk3dView_Reset.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.Sk3dView.Reset%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Internals/Helpers/3D/Sk3dView.cs/#L89">View Source</a>
  </span>
  <a id="DrawnUi_Draw_Sk3dView_Reset_" data-uid="DrawnUi.Draw.Sk3dView.Reset*"></a>
  <h4 id="DrawnUi_Draw_Sk3dView_Reset" data-uid="DrawnUi.Draw.Sk3dView.Reset">Reset()</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.Sk3dView.yml" sourcestartlinenumber="1">Resets the current state and clears all saved states</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Reset()</code></pre>
  </div>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_Sk3dView_Restore.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.Sk3dView.Restore%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Internals/Helpers/3D/Sk3dView.cs/#L56">View Source</a>
  </span>
  <a id="DrawnUi_Draw_Sk3dView_Restore_" data-uid="DrawnUi.Draw.Sk3dView.Restore*"></a>
  <h4 id="DrawnUi_Draw_Sk3dView_Restore" data-uid="DrawnUi.Draw.Sk3dView.Restore">Restore()</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.Sk3dView.yml" sourcestartlinenumber="1">Restores the previously saved transformation state</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Restore()</code></pre>
  </div>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_Sk3dView_RotateXDegrees_System_Single_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.Sk3dView.RotateXDegrees(System.Single)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Internals/Helpers/3D/Sk3dView.cs/#L114">View Source</a>
  </span>
  <a id="DrawnUi_Draw_Sk3dView_RotateXDegrees_" data-uid="DrawnUi.Draw.Sk3dView.RotateXDegrees*"></a>
  <h4 id="DrawnUi_Draw_Sk3dView_RotateXDegrees_System_Single_" data-uid="DrawnUi.Draw.Sk3dView.RotateXDegrees(System.Single)">RotateXDegrees(float)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.Sk3dView.yml" sourcestartlinenumber="1">Rotates around the X axis</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void RotateXDegrees(float degrees)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></td>
        <td><span class="parametername">degrees</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_Sk3dView_RotateYDegrees_System_Single_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.Sk3dView.RotateYDegrees(System.Single)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Internals/Helpers/3D/Sk3dView.cs/#L123">View Source</a>
  </span>
  <a id="DrawnUi_Draw_Sk3dView_RotateYDegrees_" data-uid="DrawnUi.Draw.Sk3dView.RotateYDegrees*"></a>
  <h4 id="DrawnUi_Draw_Sk3dView_RotateYDegrees_System_Single_" data-uid="DrawnUi.Draw.Sk3dView.RotateYDegrees(System.Single)">RotateYDegrees(float)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.Sk3dView.yml" sourcestartlinenumber="1">Rotates around the Y axis</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void RotateYDegrees(float degrees)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></td>
        <td><span class="parametername">degrees</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_Sk3dView_RotateZDegrees_System_Single_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.Sk3dView.RotateZDegrees(System.Single)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Internals/Helpers/3D/Sk3dView.cs/#L132">View Source</a>
  </span>
  <a id="DrawnUi_Draw_Sk3dView_RotateZDegrees_" data-uid="DrawnUi.Draw.Sk3dView.RotateZDegrees*"></a>
  <h4 id="DrawnUi_Draw_Sk3dView_RotateZDegrees_System_Single_" data-uid="DrawnUi.Draw.Sk3dView.RotateZDegrees(System.Single)">RotateZDegrees(float)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.Sk3dView.yml" sourcestartlinenumber="1">Rotates around the Z axis</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void RotateZDegrees(float degrees)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></td>
        <td><span class="parametername">degrees</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_Sk3dView_Save.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.Sk3dView.Save%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Internals/Helpers/3D/Sk3dView.cs/#L39">View Source</a>
  </span>
  <a id="DrawnUi_Draw_Sk3dView_Save_" data-uid="DrawnUi.Draw.Sk3dView.Save*"></a>
  <h4 id="DrawnUi_Draw_Sk3dView_Save" data-uid="DrawnUi.Draw.Sk3dView.Save">Save()</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.Sk3dView.yml" sourcestartlinenumber="1">Saves the current transformation state</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Save()</code></pre>
  </div>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_Sk3dView_Translate_System_Single_System_Single_System_Single_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.Sk3dView.Translate(System.Single%2CSystem.Single%2CSystem.Single)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Internals/Helpers/3D/Sk3dView.cs/#L168">View Source</a>
  </span>
  <a id="DrawnUi_Draw_Sk3dView_Translate_" data-uid="DrawnUi.Draw.Sk3dView.Translate*"></a>
  <h4 id="DrawnUi_Draw_Sk3dView_Translate_System_Single_System_Single_System_Single_" data-uid="DrawnUi.Draw.Sk3dView.Translate(System.Single,System.Single,System.Single)">Translate(float, float, float)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.Sk3dView.yml" sourcestartlinenumber="1">Translates along all axes</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Translate(float x, float y, float z)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></td>
        <td><span class="parametername">x</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></td>
        <td><span class="parametername">y</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></td>
        <td><span class="parametername">z</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_Sk3dView_TranslateX_System_Single_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.Sk3dView.TranslateX(System.Single)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Internals/Helpers/3D/Sk3dView.cs/#L141">View Source</a>
  </span>
  <a id="DrawnUi_Draw_Sk3dView_TranslateX_" data-uid="DrawnUi.Draw.Sk3dView.TranslateX*"></a>
  <h4 id="DrawnUi_Draw_Sk3dView_TranslateX_System_Single_" data-uid="DrawnUi.Draw.Sk3dView.TranslateX(System.Single)">TranslateX(float)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.Sk3dView.yml" sourcestartlinenumber="1">Translates along the X axis</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void TranslateX(float value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_Sk3dView_TranslateY_System_Single_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.Sk3dView.TranslateY(System.Single)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Internals/Helpers/3D/Sk3dView.cs/#L150">View Source</a>
  </span>
  <a id="DrawnUi_Draw_Sk3dView_TranslateY_" data-uid="DrawnUi.Draw.Sk3dView.TranslateY*"></a>
  <h4 id="DrawnUi_Draw_Sk3dView_TranslateY_System_Single_" data-uid="DrawnUi.Draw.Sk3dView.TranslateY(System.Single)">TranslateY(float)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.Sk3dView.yml" sourcestartlinenumber="1">Translates along the Y axis</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void TranslateY(float value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_Sk3dView_TranslateZ_System_Single_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.Sk3dView.TranslateZ(System.Single)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Internals/Helpers/3D/Sk3dView.cs/#L159">View Source</a>
  </span>
  <a id="DrawnUi_Draw_Sk3dView_TranslateZ_" data-uid="DrawnUi.Draw.Sk3dView.TranslateZ*"></a>
  <h4 id="DrawnUi_Draw_Sk3dView_TranslateZ_System_Single_" data-uid="DrawnUi.Draw.Sk3dView.TranslateZ(System.Single)">TranslateZ(float)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.Sk3dView.yml" sourcestartlinenumber="1">Translates along the Z axis</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void TranslateZ(float value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="extensionmethods">Extension Methods</h3>
  <div>
      <a class="xref" href="DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_">InternalExtensions.FromPlatform(object)</a>
  </div>

</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                  <li>
                    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_Sk3dView.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.Sk3dView%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A" class="contribution-link">Edit this page</a>
                  </li>
                  <li>
                    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Internals/Helpers/3D/Sk3dView.cs/#L10" class="contribution-link">View Source</a>
                  </li>
                </ul>
              </div>
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In this article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      
      <span>Generated by <strong>DocFX</strong></span>
          </div>
        </div>
      </footer>
    </div>

    <script type="text/javascript" src="../styles/docfx.vendor.min.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
