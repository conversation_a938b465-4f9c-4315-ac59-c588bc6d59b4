<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
      <title>Layout System Architecture | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width">
      <meta name="title" content="Layout System Architecture | DrawnUi Documentation ">
    
    
      <link rel="shortcut icon" href="../../images/favicon.ico">
      <link rel="stylesheet" href="../../styles/docfx.vendor.min.css">
      <link rel="stylesheet" href="../../styles/docfx.css">
      <link rel="stylesheet" href="../../styles/main.css">
      <meta property="docfx:navrel" content="../../toc.html">
      <meta property="docfx:tocrel" content="toc.html">
    
    <meta property="docfx:rel" content="../../">
    
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="../../index.html">
                <img id="logo" class="svg" src="../../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first=First data-prev=Previous data-next=Next data-last=Last></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">

        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="">
<h1 id="layout-system-architecture" sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="1">Layout System Architecture</h1>

<p sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="3">This article covers the internal architecture of DrawnUi.Maui's layout system, designed for developers who want to understand how layouts work under the hood or extend the system with custom layout types.</p>
<h2 id="layout-system-overview" sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="5">Layout System Overview</h2>
<p sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="7">DrawnUi.Maui's layout system is built on a core principle: direct rendering to canvas with optimizations for mobile and desktop platforms. Unlike traditional MAUI layouts that create native UI elements, DrawnUi.Maui renders everything using SkiaSharp, enabling consistent cross-platform visuals and better performance for complex UIs.</p>
<h2 id="core-components" sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="9">Core Components</h2>
<h3 id="skiacontrol" sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="11">SkiaControl</h3>
<p sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="13"><code sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="13">SkiaControl</code> is the foundation of the entire UI system. It provides core capabilities for:</p>
<ul sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="15">
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="15">Position tracking in the rendering tree</li>
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="16">Coordinate transformation for touch and rendering</li>
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="17">Efficient invalidation system</li>
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="18">Support for effects and transforms</li>
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="19">Hit testing and touch input handling</li>
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="20">Visibility management</li>
</ul>
<p sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="22">Its key methods include:</p>
<ul sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="23">
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="23"><code sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="23">OnMeasure</code>: Determines the size requirements of the control</li>
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="24"><code sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="24">OnArrange</code>: Positions the control within its parent</li>
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="25"><code sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="25">OnDraw</code>: Renders the control using a SkiaSharp canvas</li>
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="26"><code sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="26">InvalidateInternal</code>: Manages rendering invalidation</li>
</ul>
<h3 id="skialayout" sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="28">SkiaLayout</h3>
<p sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="30"><code sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="30">SkiaLayout</code> extends <code sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="30">SkiaControl</code> to provide layout functionality. It's implemented as a partial class with functionality split across files by layout type:</p>
<ul sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="32">
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="32"><strong sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="32">SkiaLayout.cs</strong>: Core layout mechanisms</li>
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="33"><strong sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="33">SkiaLayout.Grid.cs</strong>: Grid layout implementation</li>
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="34"><strong sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="34">SkiaLayout.ColumnRow.cs</strong>: Stack-like layouts</li>
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="35"><strong sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="35">SkiaLayout.BuildWrapLayout.cs</strong>: Wrap layout implementation</li>
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="36"><strong sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="36">SkiaLayout.ListView.cs</strong>: Virtualized list rendering</li>
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="37"><strong sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="37">SkiaLayout.IList.cs</strong>: List-specific optimization</li>
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="38"><strong sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="38">SkiaLayout.ViewsAdapter.cs</strong>: Template management</li>
</ul>
<p sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="40">This approach allows specialized handling for each layout type while sharing common infrastructure.</p>
<h3 id="layout-structures" sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="42">Layout Structures</h3>
<p sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="44">The system uses specialized structures to efficiently track and manage layout calculations:</p>
<ul sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="46">
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="46"><strong sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="46">LayoutStructure</strong>: Tracks arranged controls in stack layouts</li>
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="47"><strong sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="47">GridStructure</strong>: Manages grid-specific layout information</li>
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="48"><strong sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="48">ControlInStack</strong>: Contains information about a control's position</li>
</ul>
<h2 id="advanced-concepts" sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="50">Advanced Concepts</h2>
<h3 id="virtualization" sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="52">Virtualization</h3>
<p sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="54">Virtualization is a key performance optimization that only renders items currently visible in the viewport. This enables efficient rendering of large collections.</p>
<p sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="56">The <code sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="56">VirtualizationMode</code> enum defines several strategies:</p>
<ul sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="57">
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="57"><strong sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="57">None</strong>: All items are rendered</li>
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="58"><strong sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="58">Enabled</strong>: Only visible items are rendered and measured</li>
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="59"><strong sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="59">Smart</strong>: Renders visible items plus a buffer</li>
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="60"><strong sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="60">Managed</strong>: Uses a managed renderer for advanced cases</li>
</ul>
<p sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="62">Virtualization works alongside template recycling to minimize both CPU and memory usage.</p>
<h3 id="template-recycling" sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="64">Template Recycling</h3>
<p sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="66">The <code sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="66">RecyclingTemplate</code> property determines how templates are reused across items:</p>
<ul sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="67">
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="67"><strong sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="67">None</strong>: New instance created for each item</li>
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="68"><strong sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="68">Enabled</strong>: Templates are reused as items scroll out of view</li>
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="69"><strong sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="69">Smart</strong>: Reuses templates with additional optimizations</li>
</ul>
<p sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="71">The <code sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="71">ViewsAdapter</code> class manages template instantiation, recycling, and state management.</p>
<h3 id="measurement-strategies" sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="73">Measurement Strategies</h3>
<p sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="75">The layout system supports different strategies for measuring item sizes:</p>
<ul sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="77">
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="77"><strong sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="77">MeasureFirst</strong>: Measures all items before rendering</li>
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="78"><strong sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="78">MeasureAll</strong>: Continuously measures all items</li>
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="79"><strong sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="79">MeasureVisible</strong>: Only measures visible items</li>
</ul>
<p sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="81">These strategies let you balance between layout accuracy and performance.</p>
<h2 id="extending-the-layout-system" sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="83">Extending the Layout System</h2>
<h3 id="creating-a-custom-layout-type" sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="85">Creating a Custom Layout Type</h3>
<p sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="87">To create a custom layout type, you'll typically:</p>
<ol sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="89">
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="89">Create a new class inheriting from <code sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="89">SkiaLayout</code></li>
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="90">Override the <code sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="90">OnMeasure</code> and <code sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="90">OnArrange</code> methods</li>
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="91">Implement custom measurement and arrangement logic</li>
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="92">Optionally create custom properties for layout configuration</li>
</ol>
<p sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="94">Here's a simplified example of a circular layout implementation:</p>
<pre><code class="lang-csharp" sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="96">public class CircularLayout : SkiaLayout
{
    public static readonly BindableProperty RadiusProperty = 
        BindableProperty.Create(nameof(Radius), typeof(float), typeof(CircularLayout), 100f,
        propertyChanged: (b, o, n) =&gt; ((CircularLayout)b).InvalidateMeasure());
        
    public float Radius
    {
        get =&gt; (float)GetValue(RadiusProperty);
        set =&gt; SetValue(RadiusProperty, value);
    }
    
    protected override SKSize OnMeasure(float widthConstraint, float heightConstraint)
    {
        // Need enough space for a circle with our radius
        return new SKSize(Radius * 2, Radius * 2);
    }
    
    protected override void OnArrange(SKRect destination)
    {
        base.OnArrange(destination);
        
        // Skip if no children
        if (Children.Count == 0) return;
        
        // Calculate center point
        SKPoint center = new SKPoint(destination.MidX, destination.MidY);
        float angleStep = 360f / Children.Count;
        
        // Position each child around the circle
        for (int i = 0; i &lt; Children.Count; i++)
        {
            var child = Children[i];
            if (!child.IsVisible) continue;
            
            // Calculate position on circle
            float angle = i * angleStep * (float)Math.PI / 180f;
            float x = center.X + Radius * (float)Math.Cos(angle) - child.MeasuredSize.Width / 2;
            float y = center.Y + Radius * (float)Math.Sin(angle) - child.MeasuredSize.Height / 2;
            
            // Arrange child at calculated position
            child.Arrange(new SKRect(x, y, x + child.MeasuredSize.Width, y + child.MeasuredSize.Height));
        }
    }
}
</code></pre>
<h3 id="best-practices-for-custom-layouts" sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="144">Best Practices for Custom Layouts</h3>
<ol sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="146">
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="146"><p sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="146"><strong sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="146">Minimize Measure Calls</strong>: Measure operations are expensive. Cache results when possible.</p>
</li>
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="148"><p sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="148"><strong sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="148">Implement Proper Invalidation</strong>: Ensure your layout properly invalidates when properties affecting layout change.</p>
</li>
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="150"><p sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="150"><strong sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="150">Consider Virtualization</strong>: For layouts with many items, implement virtualization to only render visible content.</p>
</li>
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="152"><p sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="152"><strong sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="152">Optimize Arrangement Logic</strong>: Keep arrangement logic simple and efficient, especially for layouts that update frequently.</p>
</li>
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="154"><p sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="154"><strong sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="154">Respect Constraints</strong>: Always respect the width and height constraints passed to OnMeasure.</p>
</li>
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="156"><p sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="156"><strong sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="156">Cache Layout Calculations</strong>: For complex layouts, consider caching calculations that don't need to be redone every frame.</p>
</li>
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="158"><p sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="158"><strong sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="158">Extend SkiaLayout</strong>: Instead of creating entirely new layout types, consider extending SkiaLayout and creating a new LayoutType enum value if needed.</p>
</li>
</ol>
<h2 id="layout-system-internals" sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="160">Layout System Internals</h2>
<h3 id="the-layout-process" sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="162">The Layout Process</h3>
<p sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="164">The layout process follows these steps:</p>
<ol sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="166">
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="166"><strong sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="166">Parent Invalidates Layout</strong>: When a change requires remeasurement</li>
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="167"><strong sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="167">OnMeasure Called</strong>: Layout determines its size requirements</li>
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="168"><strong sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="168">Parent Determines Size</strong>: Parent decides actual size allocation</li>
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="169"><strong sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="169">OnArrange Called</strong>: Layout positions itself and its children</li>
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="170"><strong sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="170">OnDraw Called</strong>: Layout renders itself and its children</li>
</ol>
<h3 id="coordinate-spaces" sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="172">Coordinate Spaces</h3>
<p sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="174">The layout system deals with multiple coordinate spaces:</p>
<ul sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="176">
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="176"><strong sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="176">Local Space</strong>: Relative to the control itself (0,0 is top-left of control)</li>
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="177"><strong sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="177">Parent Space</strong>: Relative to the parent control</li>
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="178"><strong sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="178">Canvas Space</strong>: Relative to the drawing canvas</li>
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="179"><strong sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="179">Screen Space</strong>: Relative to the screen (used for touch input)</li>
</ul>
<p sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="181">The system provides methods for converting between these spaces, making it easier to handle positioning and hit testing.</p>
<h3 id="layout-specific-properties" sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="183">Layout-Specific Properties</h3>
<p sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="185">Layout controls have unique bindable properties that affect their behavior:</p>
<ul sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="187">
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="187"><strong sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="187">ColumnDefinitions/RowDefinitions</strong>: Define grid structure</li>
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="188"><strong sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="188">Spacing</strong>: Controls space between items</li>
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="189"><strong sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="189">Padding</strong>: Controls space inside the layout edges</li>
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="190"><strong sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="190">LayoutType</strong>: Determines layout strategy</li>
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="191"><strong sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="191">ItemsSource/ItemTemplate</strong>: For data-driven layouts</li>
</ul>
<h2 id="performance-considerations" sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="193">Performance Considerations</h2>
<h3 id="rendering-optimization" sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="195">Rendering Optimization</h3>
<p sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="197">The rendering system is optimized using several techniques:</p>
<ol sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="199">
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="199"><strong sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="199">Clipping</strong>: Only renders content within visible bounds</li>
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="200"><strong sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="200">Caching</strong>: Different caching strategies for balancing performance</li>
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="201"><strong sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="201">Background Processing</strong>: Template initialization on background threads</li>
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="202"><strong sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="202">Incremental Loading</strong>: Loading and measuring items incrementally</li>
</ol>
<h3 id="when-to-use-each-layout-type" sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="204">When to Use Each Layout Type</h3>
<ul sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="206">
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="206"><strong sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="206">Absolute</strong>: When precise positioning is needed (graphs, custom visualizations)</li>
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="207"><strong sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="207">Grid</strong>: For tabular data and form layouts</li>
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="208"><strong sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="208">Column/Row</strong>: For sequential content in one direction</li>
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="209"><strong sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="209">Wrap</strong>: For content that should flow naturally across lines (tags, flow layouts)</li>
</ul>
<h2 id="debugging-layouts" sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="211">Debugging Layouts</h2>
<p sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="213">For debugging layout issues, use these built-in features:</p>
<ul sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="215">
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="215">Set <code sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="215">IsDebugRenderBounds</code> to <code sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="215">true</code> to visualize layout boundaries</li>
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="216">Use <code sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="216">SkiaLabelFps</code> to monitor rendering performance</li>
<li sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="217">Add the <code sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="217">DebugRenderGraph</code> control to visualize the rendering tree</li>
</ul>
<h2 id="summary" sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="219">Summary</h2>
<p sourcefile="articles/advanced/layout-system.md" sourcestartlinenumber="221">DrawnUi.Maui's layout system provides a foundation for creating high-performance, visually consistent UIs across platforms. By understanding its architecture, you can leverage its capabilities to create custom layouts and optimize your application's performance.</p>

</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                  <li>
                    <a href="https://github.com/taublast/DrawnUi/blob/master/docs/articles/advanced/layout-system.md/#L1" class="contribution-link">Edit this page</a>
                  </li>
                </ul>
              </div>
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In this article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      
      <span>Generated by <strong>DocFX</strong></span>
          </div>
        </div>
      </footer>
    </div>

    <script type="text/javascript" src="../../styles/docfx.vendor.min.js"></script>
    <script type="text/javascript" src="../../styles/docfx.js"></script>
    <script type="text/javascript" src="../../styles/main.js"></script>
  </body>
</html>
