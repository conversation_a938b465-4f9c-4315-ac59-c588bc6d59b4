<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
      <title>Class FluentExtensions | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width">
      <meta name="title" content="Class FluentExtensions | DrawnUi Documentation ">
    
    <meta name="description" content="Provides extension methods for fluent API design pattern with DrawnUI controls">
      <link rel="shortcut icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../styles/docfx.vendor.min.css">
      <link rel="stylesheet" href="../styles/docfx.css">
      <link rel="stylesheet" href="../styles/main.css">
      <meta property="docfx:navrel" content="../toc.html">
      <meta property="docfx:tocrel" content="toc.html">
    
    <meta property="docfx:rel" content="../">
    
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first=First data-prev=Previous data-next=Next data-last=Last></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">

        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="DrawnUi.Draw.FluentExtensions">



  <h1 id="DrawnUi_Draw_FluentExtensions" data-uid="DrawnUi.Draw.FluentExtensions" class="text-break">Class FluentExtensions</h1>
  <div class="markdown level0 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Provides extension methods for fluent API design pattern with DrawnUI controls</p>
</div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></div>
    <div class="level1"><span class="xref">FluentExtensions</span></div>
  </div>
  <div class="inheritedMembers">
    <h5>Inherited Members</h5>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)">object.Equals(object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)">object.Equals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gethashcode">object.GetHashCode()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gettype">object.GetType()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone">object.MemberwiseClone()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.referenceequals">object.ReferenceEquals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.tostring">object.ToString()</a>
    </div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="DrawnUi.html">DrawnUi</a>.<a class="xref" href="DrawnUi.Draw.html">Draw</a></h6>
  <h6><strong>Assembly</strong>: DrawnUi.Maui.dll</h6>
  <h5 id="DrawnUi_Draw_FluentExtensions_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static class FluentExtensions</code></pre>
  </div>
  <h3 id="methods">Methods
</h3>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_Adapt__1___0_System_Action___0__.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.Adapt%60%601(%60%600%2CSystem.Action%7B%60%600%7D)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs/#L48">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_Adapt_" data-uid="DrawnUi.Draw.FluentExtensions.Adapt*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_Adapt__1___0_System_Action___0__" data-uid="DrawnUi.Draw.FluentExtensions.Adapt``1(``0,System.Action{``0})">Adapt&lt;T&gt;(T, Action&lt;T&gt;)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Performs an action on the control and returns it to continue the fluent chain</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T Adapt&lt;T&gt;(this T view, Action&lt;T&gt; action) where T : SkiaControl</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">view</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control to act upon</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.action-1">Action</a>&lt;T&gt;</td>
        <td><span class="parametername">action</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The action to perform</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control for chaining</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Type of SkiaControl</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_AssignNative__1___0___0__.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.AssignNative%60%601(%60%600%2C%60%600%40)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Internals/Extensions/FluentExtensions.Maui.cs/#L13">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_AssignNative_" data-uid="DrawnUi.Draw.FluentExtensions.AssignNative*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_AssignNative__1___0___0__" data-uid="DrawnUi.Draw.FluentExtensions.AssignNative``1(``0,``0@)">AssignNative&lt;T&gt;(T, out T)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T AssignNative&lt;T&gt;(this T control, out T variable) where T : VisualElement</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">control</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">variable</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_AssignParent__1___0_DrawnUi_Draw_SkiaControl_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.AssignParent%60%601(%60%600%2CDrawnUi.Draw.SkiaControl)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs/#L35">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_AssignParent_" data-uid="DrawnUi.Draw.FluentExtensions.AssignParent*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_AssignParent__1___0_DrawnUi_Draw_SkiaControl_" data-uid="DrawnUi.Draw.FluentExtensions.AssignParent``1(``0,DrawnUi.Draw.SkiaControl)">AssignParent&lt;T&gt;(T, SkiaControl)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Assigns the control to a parent and returns the control for fluent chaining.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T AssignParent&lt;T&gt;(this T control, SkiaControl parent) where T : SkiaControl</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">control</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control to assign</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></td>
        <td><span class="parametername">parent</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The parent control to add to</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control for chaining</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Type of SkiaControl</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_Assign__1___0___0__.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.Assign%60%601(%60%600%2C%60%600%40)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs/#L22">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_Assign_" data-uid="DrawnUi.Draw.FluentExtensions.Assign*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_Assign__1___0___0__" data-uid="DrawnUi.Draw.FluentExtensions.Assign``1(``0,``0@)">Assign&lt;T&gt;(T, out T)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Assigns the control to a variable and returns the control to continue the fluent chain</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T Assign&lt;T&gt;(this T control, out T variable) where T : SkiaControl</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">control</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control to assign</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">variable</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The out variable to store the reference</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control for chaining</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Type of SkiaControl</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_BindProperty__1___0_Microsoft_Maui_Controls_BindableProperty_System_ComponentModel_INotifyPropertyChanged_System_String_Microsoft_Maui_Controls_BindingMode_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.BindProperty%60%601(%60%600%2CMicrosoft.Maui.Controls.BindableProperty%2CSystem.ComponentModel.INotifyPropertyChanged%2CSystem.String%2CMicrosoft.Maui.Controls.BindingMode)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Internals/Extensions/FluentExtensions.Maui.cs/#L183">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_BindProperty_" data-uid="DrawnUi.Draw.FluentExtensions.BindProperty*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_BindProperty__1___0_Microsoft_Maui_Controls_BindableProperty_System_ComponentModel_INotifyPropertyChanged_System_String_Microsoft_Maui_Controls_BindingMode_" data-uid="DrawnUi.Draw.FluentExtensions.BindProperty``1(``0,Microsoft.Maui.Controls.BindableProperty,System.ComponentModel.INotifyPropertyChanged,System.String,Microsoft.Maui.Controls.BindingMode)">BindProperty&lt;T&gt;(T, BindableProperty, INotifyPropertyChanged, string, BindingMode)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Binds a property of a view to a source property using a specified path and binding mode.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T BindProperty&lt;T&gt;(this T view, BindableProperty targetProperty, INotifyPropertyChanged source, string path, BindingMode mode = BindingMode.Default) where T : SkiaControl</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">view</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The UI element that will have its property bound to a source property.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></td>
        <td><span class="parametername">targetProperty</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The property of the view that will receive the binding.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.componentmodel.inotifypropertychanged">INotifyPropertyChanged</a></td>
        <td><span class="parametername">source</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The object that implements property change notifications and serves as the data source.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></td>
        <td><span class="parametername">path</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The path to the property on the source object that will be bound to the target property.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindingmode">BindingMode</a></td>
        <td><span class="parametername">mode</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Specifies the binding mode, determining how the source and target properties interact.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Returns the view after setting up the binding.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Represents a type that extends SkiaControl, allowing for binding operations on UI elements.</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_BindProperty__1___0_Microsoft_Maui_Controls_BindableProperty_System_String_Microsoft_Maui_Controls_BindingMode_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.BindProperty%60%601(%60%600%2CMicrosoft.Maui.Controls.BindableProperty%2CSystem.String%2CMicrosoft.Maui.Controls.BindingMode)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Internals/Extensions/FluentExtensions.Maui.cs/#L166">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_BindProperty_" data-uid="DrawnUi.Draw.FluentExtensions.BindProperty*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_BindProperty__1___0_Microsoft_Maui_Controls_BindableProperty_System_String_Microsoft_Maui_Controls_BindingMode_" data-uid="DrawnUi.Draw.FluentExtensions.BindProperty``1(``0,Microsoft.Maui.Controls.BindableProperty,System.String,Microsoft.Maui.Controls.BindingMode)">BindProperty&lt;T&gt;(T, BindableProperty, string, BindingMode)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Sets up a simple non-compiled binding for a property</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T BindProperty&lt;T&gt;(this T view, BindableProperty property, string path, BindingMode mode = BindingMode.Default) where T : SkiaControl</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">view</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control to set the binding for</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></td>
        <td><span class="parametername">property</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></td>
        <td><span class="parametername">path</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The binding path</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindingmode">BindingMode</a></td>
        <td><span class="parametername">mode</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The binding mode</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control for chaining</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Type of SkiaControl</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_BindProperty__2___0_Microsoft_Maui_Controls_BindableProperty_System_String_Microsoft_Maui_Controls_IValueConverter_System_Object_Microsoft_Maui_Controls_BindingMode_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.BindProperty%60%602(%60%600%2CMicrosoft.Maui.Controls.BindableProperty%2CSystem.String%2CMicrosoft.Maui.Controls.IValueConverter%2CSystem.Object%2CMicrosoft.Maui.Controls.BindingMode)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Internals/Extensions/FluentExtensions.Maui.cs/#L207">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_BindProperty_" data-uid="DrawnUi.Draw.FluentExtensions.BindProperty*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_BindProperty__2___0_Microsoft_Maui_Controls_BindableProperty_System_String_Microsoft_Maui_Controls_IValueConverter_System_Object_Microsoft_Maui_Controls_BindingMode_" data-uid="DrawnUi.Draw.FluentExtensions.BindProperty``2(``0,Microsoft.Maui.Controls.BindableProperty,System.String,Microsoft.Maui.Controls.IValueConverter,System.Object,Microsoft.Maui.Controls.BindingMode)">BindProperty&lt;T, TProperty&gt;(T, BindableProperty, string, IValueConverter, object, BindingMode)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Sets up a simple binding for a property with a converter</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T BindProperty&lt;T, TProperty&gt;(this T view, BindableProperty targetProperty, string path, IValueConverter converter, object converterParameter = null, BindingMode mode = BindingMode.Default) where T : SkiaControl</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">view</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control to set the binding for</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></td>
        <td><span class="parametername">targetProperty</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The target property</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></td>
        <td><span class="parametername">path</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The binding path</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ivalueconverter">IValueConverter</a></td>
        <td><span class="parametername">converter</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The value converter</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></td>
        <td><span class="parametername">converterParameter</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The converter parameter</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindingmode">BindingMode</a></td>
        <td><span class="parametername">mode</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The binding mode</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control for chaining</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Type of SkiaControl</p>
</td>
      </tr>
      <tr>
        <td><span class="parametername">TProperty</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Type of the property</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_CenterX__1___0_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.CenterX%60%601(%60%600)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs/#L1218">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_CenterX_" data-uid="DrawnUi.Draw.FluentExtensions.CenterX*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_CenterX__1___0_" data-uid="DrawnUi.Draw.FluentExtensions.CenterX``1(``0)">CenterX&lt;T&gt;(T)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Sets the control's horizontal options to center</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T CenterX&lt;T&gt;(this T view) where T : SkiaControl</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">view</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control to center horizontally</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control for chaining</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Type of SkiaControl</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_CenterY__1___0_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.CenterY%60%601(%60%600)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs/#L1230">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_CenterY_" data-uid="DrawnUi.Draw.FluentExtensions.CenterY*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_CenterY__1___0_" data-uid="DrawnUi.Draw.FluentExtensions.CenterY``1(``0)">CenterY&lt;T&gt;(T)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Sets the control's vertical options to center</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T CenterY&lt;T&gt;(this T view) where T : SkiaControl</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">view</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control to center vertically</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control for chaining</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Type of SkiaControl</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_Center__1___0_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.Center%60%601(%60%600)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs/#L1304">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_Center_" data-uid="DrawnUi.Draw.FluentExtensions.Center*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_Center__1___0_" data-uid="DrawnUi.Draw.FluentExtensions.Center``1(``0)">Center&lt;T&gt;(T)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Centers the control both horizontally and vertically</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T Center&lt;T&gt;(this T view) where T : SkiaControl</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">view</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control to center</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control for chaining</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Type of SkiaControl</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_EndX__1___0_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.EndX%60%601(%60%600)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs/#L1261">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_EndX_" data-uid="DrawnUi.Draw.FluentExtensions.EndX*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_EndX__1___0_" data-uid="DrawnUi.Draw.FluentExtensions.EndX``1(``0)">EndX&lt;T&gt;(T)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T EndX&lt;T&gt;(this T view) where T : SkiaControl</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">view</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_EndY__1___0_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.EndY%60%601(%60%600)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs/#L1267">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_EndY_" data-uid="DrawnUi.Draw.FluentExtensions.EndY*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_EndY__1___0_" data-uid="DrawnUi.Draw.FluentExtensions.EndY``1(``0)">EndY&lt;T&gt;(T)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T EndY&lt;T&gt;(this T view) where T : SkiaControl</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">view</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_FillX__1___0_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.FillX%60%601(%60%600)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs/#L1255">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_FillX_" data-uid="DrawnUi.Draw.FluentExtensions.FillX*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_FillX__1___0_" data-uid="DrawnUi.Draw.FluentExtensions.FillX``1(``0)">FillX&lt;T&gt;(T)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Fills horizontally</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T FillX&lt;T&gt;(this T view) where T : SkiaControl</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">view</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_FillY__1___0_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.FillY%60%601(%60%600)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs/#L1292">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_FillY_" data-uid="DrawnUi.Draw.FluentExtensions.FillY*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_FillY__1___0_" data-uid="DrawnUi.Draw.FluentExtensions.FillY``1(``0)">FillY&lt;T&gt;(T)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Fills vertically</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T FillY&lt;T&gt;(this T view) where T : SkiaControl</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">view</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_Fill__1___0_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.Fill%60%601(%60%600)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs/#L1242">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_Fill_" data-uid="DrawnUi.Draw.FluentExtensions.Fill*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_Fill__1___0_" data-uid="DrawnUi.Draw.FluentExtensions.Fill``1(``0)">Fill&lt;T&gt;(T)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Fills in both directions</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T Fill&lt;T&gt;(this T view) where T : SkiaControl</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">view</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_Height__1___0_System_Double_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.Height%60%601(%60%600%2CSystem.Double)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs/#L1193">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_Height_" data-uid="DrawnUi.Draw.FluentExtensions.Height*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_Height__1___0_System_Double_" data-uid="DrawnUi.Draw.FluentExtensions.Height``1(``0,System.Double)">Height&lt;T&gt;(T, double)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Sets the height of a SkiaControl to a specified size and returns the modified control.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T Height&lt;T&gt;(this T view, double size) where T : SkiaControl</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">view</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control whose height is being set.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></td>
        <td><span class="parametername">size</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The new height value to be applied to the control.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The modified control with the updated height.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Represents a control that can be modified to set its height.</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_Initialize__1___0_System_Action___0__.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.Initialize%60%601(%60%600%2CSystem.Action%7B%60%600%7D)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs/#L89">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_Initialize_" data-uid="DrawnUi.Draw.FluentExtensions.Initialize*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_Initialize__1___0_System_Action___0__" data-uid="DrawnUi.Draw.FluentExtensions.Initialize``1(``0,System.Action{``0})">Initialize&lt;T&gt;(T, Action&lt;T&gt;)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Registers a callback to be executed after the control is added to the view tree and initialized.
Use for setup that requires the control to be part of the visual tree.
This is called after the control default content was created and all variables have been assigned inside the fluent chain.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T Initialize&lt;T&gt;(this T view, Action&lt;T&gt; action) where T : SkiaControl</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">view</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control to initialize</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.action-1">Action</a>&lt;T&gt;</td>
        <td><span class="parametername">action</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Initialization logic to run</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control for chaining</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Type of SkiaControl</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_ObserveBindingContext__2___0_System_Action___0___1_System_String__System_Boolean_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.ObserveBindingContext%60%602(%60%600%2CSystem.Action%7B%60%600%2C%60%601%2CSystem.String%7D%2CSystem.Boolean)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs/#L281">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_ObserveBindingContext_" data-uid="DrawnUi.Draw.FluentExtensions.ObserveBindingContext*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_ObserveBindingContext__2___0_System_Action___0___1_System_String__System_Boolean_" data-uid="DrawnUi.Draw.FluentExtensions.ObserveBindingContext``2(``0,System.Action{``0,``1,System.String},System.Boolean)">ObserveBindingContext&lt;T, TSource&gt;(T, Action&lt;T, TSource, string&gt;, bool)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Watches for property changes on the control's BindingContext of type TSource.
Works with both immediate and delayed BindingContext assignment scenarios.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T ObserveBindingContext&lt;T, TSource&gt;(this T control, Action&lt;T, TSource, string&gt; callback, bool debugTypeMismatch = true) where T : SkiaControl where TSource : INotifyPropertyChanged</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">control</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control to watch</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.action-3">Action</a>&lt;T, TSource, <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a>&gt;</td>
        <td><span class="parametername">callback</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Callback executed when properties change, receiving the control, the typed BindingContext, and the property name</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></td>
        <td><span class="parametername">debugTypeMismatch</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Whether to log a warning when the actual BindingContext type doesn't match TSource (default: true)</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control for chaining</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Type of the control</p>
</td>
      </tr>
      <tr>
        <td><span class="parametername">TSource</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Expected type of the BindingContext</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 id="DrawnUi_Draw_FluentExtensions_ObserveBindingContext__2___0_System_Action___0___1_System_String__System_Boolean__remarks">Remarks</h5>
  <div class="markdown level1 remarks"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">This method handles two scenarios:</p>
<ol sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="2">
<li sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="2">The BindingContext is already set when the method is called</li>
<li sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="3">The BindingContext will be set sometime after the method is called</li>
</ol>
<p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="5">The callback will be invoked immediately after subscription with an empty property name,
allowing initialization based on the current state.</p>
</div>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_ObserveDeepNestedProperty__5___0_System_Func___1___2__System_String_System_Func___2___3__System_String_System_Func___3___4__System_String_System_Action___0___4____4_System_Boolean_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.ObserveDeepNestedProperty%60%605(%60%600%2CSystem.Func%7B%60%601%2C%60%602%7D%2CSystem.String%2CSystem.Func%7B%60%602%2C%60%603%7D%2CSystem.String%2CSystem.Func%7B%60%603%2C%60%604%7D%2CSystem.String%2CSystem.Action%7B%60%600%2C%60%604%7D%2C%60%604%2CSystem.Boolean)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs/#L366">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_ObserveDeepNestedProperty_" data-uid="DrawnUi.Draw.FluentExtensions.ObserveDeepNestedProperty*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_ObserveDeepNestedProperty__5___0_System_Func___1___2__System_String_System_Func___2___3__System_String_System_Func___3___4__System_String_System_Action___0___4____4_System_Boolean_" data-uid="DrawnUi.Draw.FluentExtensions.ObserveDeepNestedProperty``5(``0,System.Func{``1,``2},System.String,System.Func{``2,``3},System.String,System.Func{``3,``4},System.String,System.Action{``0,``4},``4,System.Boolean)">ObserveDeepNestedProperty&lt;T, TSource, TIntermediate1, TIntermediate2, TProperty&gt;(T, Func&lt;TSource, TIntermediate1&gt;, string, Func&lt;TIntermediate1, TIntermediate2&gt;, string, Func&lt;TIntermediate2, TProperty&gt;, string, Action&lt;T, TProperty&gt;, TProperty, bool)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Observes a deeply nested property on the control's BindingContext in a type-safe manner.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T ObserveDeepNestedProperty&lt;T, TSource, TIntermediate1, TIntermediate2, TProperty&gt;(this T control, Func&lt;TSource, TIntermediate1&gt; intermediate1Selector, string intermediate1PropertyName, Func&lt;TIntermediate1, TIntermediate2&gt; intermediate2Selector, string intermediate2PropertyName, Func&lt;TIntermediate2, TProperty&gt; propertySelector, string propertyName, Action&lt;T, TProperty&gt; callback, TProperty defaultValue = default, bool debugTypeMismatch = true) where T : SkiaControl where TSource : INotifyPropertyChanged where TIntermediate1 : class, INotifyPropertyChanged where TIntermediate2 : class, INotifyPropertyChanged</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">control</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control to watch</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.func-2">Func</a>&lt;TSource, TIntermediate1&gt;</td>
        <td><span class="parametername">intermediate1Selector</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Function to select the first intermediate object</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></td>
        <td><span class="parametername">intermediate1PropertyName</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Name of the first intermediate property</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.func-2">Func</a>&lt;TIntermediate1, TIntermediate2&gt;</td>
        <td><span class="parametername">intermediate2Selector</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Function to select the second intermediate object</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></td>
        <td><span class="parametername">intermediate2PropertyName</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Name of the second intermediate property</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.func-2">Func</a>&lt;TIntermediate2, TProperty&gt;</td>
        <td><span class="parametername">propertySelector</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Function to select the final property</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></td>
        <td><span class="parametername">propertyName</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Name of the final property</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.action-2">Action</a>&lt;T, TProperty&gt;</td>
        <td><span class="parametername">callback</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Callback that receives the control and current property value</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">TProperty</span></td>
        <td><span class="parametername">defaultValue</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Default value to use when any intermediate is null</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></td>
        <td><span class="parametername">debugTypeMismatch</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Whether to log warnings for type mismatches</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control for chaining</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Type of the control</p>
</td>
      </tr>
      <tr>
        <td><span class="parametername">TSource</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Expected type of the BindingContext</p>
</td>
      </tr>
      <tr>
        <td><span class="parametername">TIntermediate1</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Type of the first intermediate object</p>
</td>
      </tr>
      <tr>
        <td><span class="parametername">TIntermediate2</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Type of the second intermediate object</p>
</td>
      </tr>
      <tr>
        <td><span class="parametername">TProperty</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Type of the final property</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_ObserveDeep__4___0_System_Func___1___2__System_String_System_Func___2___3__System_String_System_Action___0___3____3_System_Boolean_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.ObserveDeep%60%604(%60%600%2CSystem.Func%7B%60%601%2C%60%602%7D%2CSystem.String%2CSystem.Func%7B%60%602%2C%60%603%7D%2CSystem.String%2CSystem.Action%7B%60%600%2C%60%603%7D%2C%60%603%2CSystem.Boolean)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs/#L693">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_ObserveDeep_" data-uid="DrawnUi.Draw.FluentExtensions.ObserveDeep*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_ObserveDeep__4___0_System_Func___1___2__System_String_System_Func___2___3__System_String_System_Action___0___3____3_System_Boolean_" data-uid="DrawnUi.Draw.FluentExtensions.ObserveDeep``4(``0,System.Func{``1,``2},System.String,System.Func{``2,``3},System.String,System.Action{``0,``3},``3,System.Boolean)">ObserveDeep&lt;T, TSource, TIntermediate, TProperty&gt;(T, Func&lt;TSource, TIntermediate&gt;, string, Func&lt;TIntermediate, TProperty&gt;, string, Action&lt;T, TProperty&gt;, TProperty, bool)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Observes a nested property on the control's BindingContext in a type-safe manner.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T ObserveDeep&lt;T, TSource, TIntermediate, TProperty&gt;(this T control, Func&lt;TSource, TIntermediate&gt; intermediateSelector, string intermediatePropertyName, Func&lt;TIntermediate, TProperty&gt; propertySelector, string propertyName, Action&lt;T, TProperty&gt; callback, TProperty defaultValue = default, bool debugTypeMismatch = true) where T : SkiaControl where TSource : INotifyPropertyChanged where TIntermediate : class, INotifyPropertyChanged</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">control</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control to watch</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.func-2">Func</a>&lt;TSource, TIntermediate&gt;</td>
        <td><span class="parametername">intermediateSelector</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Function to select the intermediate object</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></td>
        <td><span class="parametername">intermediatePropertyName</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Name of the intermediate property</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.func-2">Func</a>&lt;TIntermediate, TProperty&gt;</td>
        <td><span class="parametername">propertySelector</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Function to select the final property</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></td>
        <td><span class="parametername">propertyName</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Name of the final property</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.action-2">Action</a>&lt;T, TProperty&gt;</td>
        <td><span class="parametername">callback</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Callback that receives the control and current property value</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">TProperty</span></td>
        <td><span class="parametername">defaultValue</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Default value to use when intermediate is null</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></td>
        <td><span class="parametername">debugTypeMismatch</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Whether to log warnings for type mismatches</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control for chaining</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Type of the control</p>
</td>
      </tr>
      <tr>
        <td><span class="parametername">TSource</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Expected type of the BindingContext</p>
</td>
      </tr>
      <tr>
        <td><span class="parametername">TIntermediate</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Type of the intermediate object</p>
</td>
      </tr>
      <tr>
        <td><span class="parametername">TProperty</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Type of the final property</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_ObserveSelf__1___0_System_Action___0_System_String__.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.ObserveSelf%60%601(%60%600%2CSystem.Action%7B%60%600%2CSystem.String%7D)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs/#L144">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_ObserveSelf_" data-uid="DrawnUi.Draw.FluentExtensions.ObserveSelf*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_ObserveSelf__1___0_System_Action___0_System_String__" data-uid="DrawnUi.Draw.FluentExtensions.ObserveSelf``1(``0,System.Action{``0,System.String})">ObserveSelf&lt;T&gt;(T, Action&lt;T, string&gt;)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Subscribes to PropertyChanged of this control, will unsubscribe upon control disposal.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T ObserveSelf&lt;T&gt;(this T view, Action&lt;T, string&gt; action) where T : SkiaControl</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">view</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.action-2">Action</a>&lt;T, <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a>&gt;</td>
        <td><span class="parametername">action</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The target control for chaining</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Type of the target control (the one being extended)</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_ObserveTargetBindingContext__3___0___1_System_Action___0___1___2_System_String__System_Boolean_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.ObserveTargetBindingContext%60%603(%60%600%2C%60%601%2CSystem.Action%7B%60%600%2C%60%601%2C%60%602%2CSystem.String%7D%2CSystem.Boolean)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs/#L600">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_ObserveTargetBindingContext_" data-uid="DrawnUi.Draw.FluentExtensions.ObserveTargetBindingContext*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_ObserveTargetBindingContext__3___0___1_System_Action___0___1___2_System_String__System_Boolean_" data-uid="DrawnUi.Draw.FluentExtensions.ObserveTargetBindingContext``3(``0,``1,System.Action{``0,``1,``2,System.String},System.Boolean)">ObserveTargetBindingContext&lt;T, TTarget, TSource&gt;(T, TTarget, Action&lt;T, TTarget, TSource, string&gt;, bool)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Watches for property changes on another control's BindingContext of type TSource.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T ObserveTargetBindingContext&lt;T, TTarget, TSource&gt;(this T control, TTarget target, Action&lt;T, TTarget, TSource, string&gt; callback, bool debugTypeMismatch = true) where T : SkiaControl where TTarget : SkiaControl where TSource : INotifyPropertyChanged</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">control</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control to extend</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">TTarget</span></td>
        <td><span class="parametername">target</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The target control whose BindingContext to watch</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.action-4">Action</a>&lt;T, TTarget, TSource, <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a>&gt;</td>
        <td><span class="parametername">callback</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Callback executed when properties change, receiving the control, the target control, the typed BindingContext, and the property name</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></td>
        <td><span class="parametername">debugTypeMismatch</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Whether to log a warning when the actual BindingContext type doesn't match TSource (default: true)</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The original control for chaining</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Type of the control being extended</p>
</td>
      </tr>
      <tr>
        <td><span class="parametername">TTarget</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Type of the target control whose BindingContext we're watching</p>
</td>
      </tr>
      <tr>
        <td><span class="parametername">TSource</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Expected type of the target control's BindingContext</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 id="DrawnUi_Draw_FluentExtensions_ObserveTargetBindingContext__3___0___1_System_Action___0___1___2_System_String__System_Boolean__remarks">Remarks</h5>
  <div class="markdown level1 remarks"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">This method handles two scenarios:</p>
<ol sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="2">
<li sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="2">The target's BindingContext is already set when the method is called</li>
<li sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="3">The target's BindingContext will be set sometime after the method is called</li>
</ol>
</div>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_ObserveTargetProperty__4___0_System_Linq_Expressions_Expression_System_Func___1___2___System_Linq_Expressions_Expression_System_Func___2___3___System_Action___0___3____3_System_Boolean_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.ObserveTargetProperty%60%604(%60%600%2CSystem.Linq.Expressions.Expression%7BSystem.Func%7B%60%601%2C%60%602%7D%7D%2CSystem.Linq.Expressions.Expression%7BSystem.Func%7B%60%602%2C%60%603%7D%7D%2CSystem.Action%7B%60%600%2C%60%603%7D%2C%60%603%2CSystem.Boolean)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs/#L879">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_ObserveTargetProperty_" data-uid="DrawnUi.Draw.FluentExtensions.ObserveTargetProperty*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_ObserveTargetProperty__4___0_System_Linq_Expressions_Expression_System_Func___1___2___System_Linq_Expressions_Expression_System_Func___2___3___System_Action___0___3____3_System_Boolean_" data-uid="DrawnUi.Draw.FluentExtensions.ObserveTargetProperty``4(``0,System.Linq.Expressions.Expression{System.Func{``1,``2}},System.Linq.Expressions.Expression{System.Func{``2,``3}},System.Action{``0,``3},``3,System.Boolean)">ObserveTargetProperty&lt;T, TSource, TIntermediate, TProperty&gt;(T, Expression&lt;Func&lt;TSource, TIntermediate&gt;&gt;, Expression&lt;Func&lt;TIntermediate, TProperty&gt;&gt;, Action&lt;T, TProperty&gt;, TProperty, bool)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Observes a nested property on the control's BindingContext using expression trees to extract property names.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T ObserveTargetProperty&lt;T, TSource, TIntermediate, TProperty&gt;(this T control, Expression&lt;Func&lt;TSource, TIntermediate&gt;&gt; intermediateSelector, Expression&lt;Func&lt;TIntermediate, TProperty&gt;&gt; propertySelector, Action&lt;T, TProperty&gt; callback, TProperty defaultValue = default, bool debugTypeMismatch = true) where T : SkiaControl where TSource : INotifyPropertyChanged where TIntermediate : class, INotifyPropertyChanged</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">control</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.linq.expressions.expression-1">Expression</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.func-2">Func</a>&lt;TSource, TIntermediate&gt;&gt;</td>
        <td><span class="parametername">intermediateSelector</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.linq.expressions.expression-1">Expression</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.func-2">Func</a>&lt;TIntermediate, TProperty&gt;&gt;</td>
        <td><span class="parametername">propertySelector</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.action-2">Action</a>&lt;T, TProperty&gt;</td>
        <td><span class="parametername">callback</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">TProperty</span></td>
        <td><span class="parametername">defaultValue</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></td>
        <td><span class="parametername">debugTypeMismatch</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="parametername">TSource</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="parametername">TIntermediate</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="parametername">TProperty</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_Observe__2___0_System_Func___1__System_Action___0_System_String__System_String___.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.Observe%60%602(%60%600%2CSystem.Func%7B%60%601%7D%2CSystem.Action%7B%60%600%2CSystem.String%7D%2CSystem.String%5B%5D)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs/#L211">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_Observe_" data-uid="DrawnUi.Draw.FluentExtensions.Observe*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_Observe__2___0_System_Func___1__System_Action___0_System_String__System_String___" data-uid="DrawnUi.Draw.FluentExtensions.Observe``2(``0,System.Func{``1},System.Action{``0,System.String},System.String[])">Observe&lt;T, TSource&gt;(T, Func&lt;TSource&gt;, Action&lt;T, string&gt;, string[])</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Observes a control that will be assigned later in the initialization process.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T Observe&lt;T, TSource&gt;(this T control, Func&lt;TSource&gt; sourceFetcher, Action&lt;T, string&gt; callback, string[] propertyFilter = null) where T : SkiaControl where TSource : SkiaControl, INotifyPropertyChanged</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">control</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control subscribing to changes</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.func-1">Func</a>&lt;TSource&gt;</td>
        <td><span class="parametername">sourceFetcher</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Function that will retrieve the source control when needed</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.action-2">Action</a>&lt;T, <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a>&gt;</td>
        <td><span class="parametername">callback</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Callback that receives the control instance and property name when changed</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a>[]</td>
        <td><span class="parametername">propertyFilter</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Optional filter to only trigger on specific properties</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The target control for chaining</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Type of the target control (the one being extended)</p>
</td>
      </tr>
      <tr>
        <td><span class="parametername">TSource</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Type of the source control (the one that will be observed)</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_Observe__2___0___1_System_Action___0_System_String__System_String___.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.Observe%60%602(%60%600%2C%60%601%2CSystem.Action%7B%60%600%2CSystem.String%7D%2CSystem.String%5B%5D)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs/#L160">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_Observe_" data-uid="DrawnUi.Draw.FluentExtensions.Observe*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_Observe__2___0___1_System_Action___0_System_String__System_String___" data-uid="DrawnUi.Draw.FluentExtensions.Observe``2(``0,``1,System.Action{``0,System.String},System.String[])">Observe&lt;T, TSource&gt;(T, TSource, Action&lt;T, string&gt;, string[])</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Subscribes to property changes on a source control and executes a callback when they occur.
Will unsubscribe upon control disposal.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T Observe&lt;T, TSource&gt;(this T control, TSource target, Action&lt;T, string&gt; callback, string[] propertyFilter = null) where T : SkiaControl where TSource : INotifyPropertyChanged</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">control</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control subscribing to changes</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">TSource</span></td>
        <td><span class="parametername">target</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control being observed</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.action-2">Action</a>&lt;T, <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a>&gt;</td>
        <td><span class="parametername">callback</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Callback that receives the property name when changed</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a>[]</td>
        <td><span class="parametername">propertyFilter</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Optional filter to only trigger on specific properties</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The target control for chaining</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Type of the target control (the one being extended)</p>
</td>
      </tr>
      <tr>
        <td><span class="parametername">TSource</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Type of the source control (the one being observed)</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_OnBindingContextSet__1___0_System_Action___0_System_Object__System_String___.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.OnBindingContextSet%60%601(%60%600%2CSystem.Action%7B%60%600%2CSystem.Object%7D%2CSystem.String%5B%5D)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs/#L118">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_OnBindingContextSet_" data-uid="DrawnUi.Draw.FluentExtensions.OnBindingContextSet*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_OnBindingContextSet__1___0_System_Action___0_System_Object__System_String___" data-uid="DrawnUi.Draw.FluentExtensions.OnBindingContextSet``1(``0,System.Action{``0,System.Object},System.String[])">OnBindingContextSet&lt;T&gt;(T, Action&lt;T, object&gt;, string[])</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Registers a callback to be executed when the control's BindingContext was set/changed.
Called inside base.ApplyBindingContext().</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T OnBindingContextSet&lt;T&gt;(this T control, Action&lt;T, object&gt; callback, string[] propertyFilter = null) where T : SkiaControl</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">control</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control to observe</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.action-2">Action</a>&lt;T, <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a>&gt;</td>
        <td><span class="parametername">callback</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Callback to execute when BindingContext is set</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a>[]</td>
        <td><span class="parametername">propertyFilter</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Optional property filter</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control for chaining</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Type of SkiaControl</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_OnLongPressing__1___0_System_Action___0__.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.OnLongPressing%60%601(%60%600%2CSystem.Action%7B%60%600%7D)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs/#L1106">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_OnLongPressing_" data-uid="DrawnUi.Draw.FluentExtensions.OnLongPressing*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_OnLongPressing__1___0_System_Action___0__" data-uid="DrawnUi.Draw.FluentExtensions.OnLongPressing``1(``0,System.Action{``0})">OnLongPressing&lt;T&gt;(T, Action&lt;T&gt;)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T OnLongPressing&lt;T&gt;(this T view, Action&lt;T&gt; action) where T : SkiaControl</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">view</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.action-1">Action</a>&lt;T&gt;</td>
        <td><span class="parametername">action</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_OnPaint__1___0_System_Action___0_DrawnUi_Draw_DrawingContext__.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.OnPaint%60%601(%60%600%2CSystem.Action%7B%60%600%2CDrawnUi.Draw.DrawingContext%7D)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs/#L103">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_OnPaint_" data-uid="DrawnUi.Draw.FluentExtensions.OnPaint*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_OnPaint__1___0_System_Action___0_DrawnUi_Draw_DrawingContext__" data-uid="DrawnUi.Draw.FluentExtensions.OnPaint``1(``0,System.Action{``0,DrawnUi.Draw.DrawingContext})">OnPaint&lt;T&gt;(T, Action&lt;T, DrawingContext&gt;)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Registers a callback to be executed during the paint phase of the control's rendering.
Called inside the base.Paint(..).</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T OnPaint&lt;T&gt;(this T view, Action&lt;T, DrawingContext&gt; action) where T : SkiaControl</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">view</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control to attach paint logic to</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.action-2">Action</a>&lt;T, <a class="xref" href="DrawnUi.Draw.DrawingContext.html">DrawingContext</a>&gt;</td>
        <td><span class="parametername">action</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Paint logic to run</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control for chaining</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Type of SkiaControl</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_OnTapped__1___0_System_Action___0__.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.OnTapped%60%601(%60%600%2CSystem.Action%7B%60%600%7D)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs/#L1092">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_OnTapped_" data-uid="DrawnUi.Draw.FluentExtensions.OnTapped*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_OnTapped__1___0_System_Action___0__" data-uid="DrawnUi.Draw.FluentExtensions.OnTapped``1(``0,System.Action{``0})">OnTapped&lt;T&gt;(T, Action&lt;T&gt;)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Uses an <code sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">AddGestures.SetCommandTapped</code> with this control, will invoke code in passed callback when tapped.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T OnTapped&lt;T&gt;(this T view, Action&lt;T&gt; action) where T : SkiaControl</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">view</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.action-1">Action</a>&lt;T&gt;</td>
        <td><span class="parametername">action</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_OnTextChanged_DrawnUi_Controls_SkiaMauiEditor_System_Action_DrawnUi_Controls_SkiaMauiEditor_System_String__.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.OnTextChanged(DrawnUi.Controls.SkiaMauiEditor%2CSystem.Action%7BDrawnUi.Controls.SkiaMauiEditor%2CSystem.String%7D)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs/#L1622">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_OnTextChanged_" data-uid="DrawnUi.Draw.FluentExtensions.OnTextChanged*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_OnTextChanged_DrawnUi_Controls_SkiaMauiEditor_System_Action_DrawnUi_Controls_SkiaMauiEditor_System_String__" data-uid="DrawnUi.Draw.FluentExtensions.OnTextChanged(DrawnUi.Controls.SkiaMauiEditor,System.Action{DrawnUi.Controls.SkiaMauiEditor,System.String})">OnTextChanged(SkiaMauiEditor, Action&lt;SkiaMauiEditor, string&gt;)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Registers a callback to be executed when the text of a SkiaMauiEditor changes.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static SkiaMauiEditor OnTextChanged(this SkiaMauiEditor control, Action&lt;SkiaMauiEditor, string&gt; action)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="DrawnUi.Controls.SkiaMauiEditor.html">SkiaMauiEditor</a></td>
        <td><span class="parametername">control</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The editor control to observe</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.action-2">Action</a>&lt;<a class="xref" href="DrawnUi.Controls.SkiaMauiEditor.html">SkiaMauiEditor</a>, <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a>&gt;</td>
        <td><span class="parametername">action</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Callback receiving the editor and new text</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="DrawnUi.Controls.SkiaMauiEditor.html">SkiaMauiEditor</a></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The editor control for chaining</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_OnTextChanged_DrawnUi_Controls_SkiaMauiEntry_System_Action_DrawnUi_Controls_SkiaMauiEntry_System_String__.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.OnTextChanged(DrawnUi.Controls.SkiaMauiEntry%2CSystem.Action%7BDrawnUi.Controls.SkiaMauiEntry%2CSystem.String%7D)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs/#L1609">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_OnTextChanged_" data-uid="DrawnUi.Draw.FluentExtensions.OnTextChanged*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_OnTextChanged_DrawnUi_Controls_SkiaMauiEntry_System_Action_DrawnUi_Controls_SkiaMauiEntry_System_String__" data-uid="DrawnUi.Draw.FluentExtensions.OnTextChanged(DrawnUi.Controls.SkiaMauiEntry,System.Action{DrawnUi.Controls.SkiaMauiEntry,System.String})">OnTextChanged(SkiaMauiEntry, Action&lt;SkiaMauiEntry, string&gt;)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Registers a callback to be executed when the text of a SkiaMauiEntry changes.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static SkiaMauiEntry OnTextChanged(this SkiaMauiEntry control, Action&lt;SkiaMauiEntry, string&gt; action)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="DrawnUi.Controls.SkiaMauiEntry.html">SkiaMauiEntry</a></td>
        <td><span class="parametername">control</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The entry control to observe</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.action-2">Action</a>&lt;<a class="xref" href="DrawnUi.Controls.SkiaMauiEntry.html">SkiaMauiEntry</a>, <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a>&gt;</td>
        <td><span class="parametername">action</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Callback receiving the entry and new text</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="DrawnUi.Controls.SkiaMauiEntry.html">SkiaMauiEntry</a></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The entry control for chaining</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_OnTextChanged_DrawnUi_Draw_SkiaLabel_System_Action_DrawnUi_Draw_SkiaLabel_System_String__.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.OnTextChanged(DrawnUi.Draw.SkiaLabel%2CSystem.Action%7BDrawnUi.Draw.SkiaLabel%2CSystem.String%7D)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs/#L1637">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_OnTextChanged_" data-uid="DrawnUi.Draw.FluentExtensions.OnTextChanged*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_OnTextChanged_DrawnUi_Draw_SkiaLabel_System_Action_DrawnUi_Draw_SkiaLabel_System_String__" data-uid="DrawnUi.Draw.FluentExtensions.OnTextChanged(DrawnUi.Draw.SkiaLabel,System.Action{DrawnUi.Draw.SkiaLabel,System.String})">OnTextChanged(SkiaLabel, Action&lt;SkiaLabel, string&gt;)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Registers a callback to be executed when the text of a SkiaLabel changes.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static SkiaLabel OnTextChanged(this SkiaLabel control, Action&lt;SkiaLabel, string&gt; action)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="DrawnUi.Draw.SkiaLabel.html">SkiaLabel</a></td>
        <td><span class="parametername">control</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The label control to observe</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.action-2">Action</a>&lt;<a class="xref" href="DrawnUi.Draw.SkiaLabel.html">SkiaLabel</a>, <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a>&gt;</td>
        <td><span class="parametername">action</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Callback receiving the label and new text</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="DrawnUi.Draw.SkiaLabel.html">SkiaLabel</a></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The label control for chaining</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_SetFontSize__1___0_System_Double_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.SetFontSize%60%601(%60%600%2CSystem.Double)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs/#L1176">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_SetFontSize_" data-uid="DrawnUi.Draw.FluentExtensions.SetFontSize*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_SetFontSize__1___0_System_Double_" data-uid="DrawnUi.Draw.FluentExtensions.SetFontSize``1(``0,System.Double)">SetFontSize&lt;T&gt;(T, double)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Sets the label font size in points (double)</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T SetFontSize&lt;T&gt;(this T view, double size) where T : SkiaLabel</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">view</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></td>
        <td><span class="parametername">size</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_SetGrid__1___0_System_Int32_System_Int32_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.SetGrid%60%601(%60%600%2CSystem.Int32%2CSystem.Int32)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Internals/Extensions/FluentExtensions.Maui.cs/#L124">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_SetGrid_" data-uid="DrawnUi.Draw.FluentExtensions.SetGrid*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_SetGrid__1___0_System_Int32_System_Int32_" data-uid="DrawnUi.Draw.FluentExtensions.SetGrid``1(``0,System.Int32,System.Int32)">SetGrid&lt;T&gt;(T, int, int)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Sets the Grid row and column in a single call</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T SetGrid&lt;T&gt;(this T view, int column, int row) where T : SkiaControl</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">view</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control to set the grid position for</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></td>
        <td><span class="parametername">column</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The column index</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></td>
        <td><span class="parametername">row</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The row index</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control for chaining</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Type of SkiaControl</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_SetGrid__1___0_System_Int32_System_Int32_System_Int32_System_Int32_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.SetGrid%60%601(%60%600%2CSystem.Int32%2CSystem.Int32%2CSystem.Int32%2CSystem.Int32)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Internals/Extensions/FluentExtensions.Maui.cs/#L141">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_SetGrid_" data-uid="DrawnUi.Draw.FluentExtensions.SetGrid*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_SetGrid__1___0_System_Int32_System_Int32_System_Int32_System_Int32_" data-uid="DrawnUi.Draw.FluentExtensions.SetGrid``1(``0,System.Int32,System.Int32,System.Int32,System.Int32)">SetGrid&lt;T&gt;(T, int, int, int, int)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Sets the Grid row, column, rowspan and columnspan in a single call</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T SetGrid&lt;T&gt;(this T view, int column, int row, int columnSpan, int rowSpan) where T : SkiaControl</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">view</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control to set the grid position for</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></td>
        <td><span class="parametername">column</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The column index</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></td>
        <td><span class="parametername">row</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The row index</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></td>
        <td><span class="parametername">columnSpan</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The number of columns to span</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></td>
        <td><span class="parametername">rowSpan</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The number of rows to span</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control for chaining</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Type of SkiaControl</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_SetMargin__1___0_System_Double_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.SetMargin%60%601(%60%600%2CSystem.Double)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs/#L1320">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_SetMargin_" data-uid="DrawnUi.Draw.FluentExtensions.SetMargin*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_SetMargin__1___0_System_Double_" data-uid="DrawnUi.Draw.FluentExtensions.SetMargin``1(``0,System.Double)">SetMargin&lt;T&gt;(T, double)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Sets the margin for the control</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T SetMargin&lt;T&gt;(this T view, double uniformMargin) where T : SkiaControl</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">view</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control to set margin for</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></td>
        <td><span class="parametername">uniformMargin</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The uniform margin to apply to all sides</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control for chaining</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Type of SkiaControl</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_SetMargin__1___0_System_Double_System_Double_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.SetMargin%60%601(%60%600%2CSystem.Double%2CSystem.Double)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs/#L1334">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_SetMargin_" data-uid="DrawnUi.Draw.FluentExtensions.SetMargin*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_SetMargin__1___0_System_Double_System_Double_" data-uid="DrawnUi.Draw.FluentExtensions.SetMargin``1(``0,System.Double,System.Double)">SetMargin&lt;T&gt;(T, double, double)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Sets the margin for the control</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T SetMargin&lt;T&gt;(this T view, double horizontal, double vertical) where T : SkiaControl</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">view</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control to set margin for</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></td>
        <td><span class="parametername">horizontal</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The left and right margin</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></td>
        <td><span class="parametername">vertical</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The top and bottom margin</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control for chaining</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Type of SkiaControl</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_SetMargin__1___0_System_Double_System_Double_System_Double_System_Double_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.SetMargin%60%601(%60%600%2CSystem.Double%2CSystem.Double%2CSystem.Double%2CSystem.Double)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs/#L1350">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_SetMargin_" data-uid="DrawnUi.Draw.FluentExtensions.SetMargin*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_SetMargin__1___0_System_Double_System_Double_System_Double_System_Double_" data-uid="DrawnUi.Draw.FluentExtensions.SetMargin``1(``0,System.Double,System.Double,System.Double,System.Double)">SetMargin&lt;T&gt;(T, double, double, double, double)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Sets the margin for the control</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T SetMargin&lt;T&gt;(this T view, double left, double top, double right, double bottom) where T : SkiaControl</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">view</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control to set margin for</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></td>
        <td><span class="parametername">left</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The left margin</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></td>
        <td><span class="parametername">top</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The top margin</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></td>
        <td><span class="parametername">right</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The right margin</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></td>
        <td><span class="parametername">bottom</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The bottom margin</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control for chaining</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Type of SkiaControl</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_SetWidth__1___0_System_Double_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.SetWidth%60%601(%60%600%2CSystem.Double)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs/#L1206">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_SetWidth_" data-uid="DrawnUi.Draw.FluentExtensions.SetWidth*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_SetWidth__1___0_System_Double_" data-uid="DrawnUi.Draw.FluentExtensions.SetWidth``1(``0,System.Double)">SetWidth&lt;T&gt;(T, double)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Sets the width of a SkiaControl to a specified size and returns the modified control.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T SetWidth&lt;T&gt;(this T view, double size) where T : SkiaControl</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">view</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control whose width is being set to a new value.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></td>
        <td><span class="parametername">size</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The new width value to be applied to the control.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The modified control with the updated width.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Represents a type that extends SkiaControl, allowing for width adjustments.</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_Shape__1___0_DrawnUi_Draw_ShapeType_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.Shape%60%601(%60%600%2CDrawnUi.Draw.ShapeType)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs/#L1533">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_Shape_" data-uid="DrawnUi.Draw.FluentExtensions.Shape*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_Shape__1___0_DrawnUi_Draw_ShapeType_" data-uid="DrawnUi.Draw.FluentExtensions.Shape``1(``0,DrawnUi.Draw.ShapeType)">Shape&lt;T&gt;(T, ShapeType)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Sets the shape type for SkiaShape (shorter alias)</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T Shape&lt;T&gt;(this T shape, ShapeType shapeType) where T : SkiaShape</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">shape</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The shape to set type for</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="DrawnUi.Draw.ShapeType.html">ShapeType</a></td>
        <td><span class="parametername">shapeType</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The shape type</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The shape for chaining</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Type of SkiaShape</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_StartX__1___0_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.StartX%60%601(%60%600)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs/#L1273">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_StartX_" data-uid="DrawnUi.Draw.FluentExtensions.StartX*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_StartX__1___0_" data-uid="DrawnUi.Draw.FluentExtensions.StartX``1(``0)">StartX&lt;T&gt;(T)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T StartX&lt;T&gt;(this T view) where T : SkiaControl</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">view</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_StartY__1___0_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.StartY%60%601(%60%600)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs/#L1279">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_StartY_" data-uid="DrawnUi.Draw.FluentExtensions.StartY*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_StartY__1___0_" data-uid="DrawnUi.Draw.FluentExtensions.StartY``1(``0)">StartY&lt;T&gt;(T)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T StartY&lt;T&gt;(this T view) where T : SkiaControl</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">view</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_WithAspect__1___0_DrawnUi_Draw_TransformAspect_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.WithAspect%60%601(%60%600%2CDrawnUi.Draw.TransformAspect)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs/#L1550">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_WithAspect_" data-uid="DrawnUi.Draw.FluentExtensions.WithAspect*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_WithAspect__1___0_DrawnUi_Draw_TransformAspect_" data-uid="DrawnUi.Draw.FluentExtensions.WithAspect``1(``0,DrawnUi.Draw.TransformAspect)">WithAspect&lt;T&gt;(T, TransformAspect)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Sets the aspect for SkiaImage</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T WithAspect&lt;T&gt;(this T image, TransformAspect aspect) where T : SkiaImage</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">image</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The image to set aspect for</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="DrawnUi.Draw.TransformAspect.html">TransformAspect</a></td>
        <td><span class="parametername">aspect</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The transform aspect</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The image for chaining</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Type of SkiaImage</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_WithBackgroundColor__1___0_Microsoft_Maui_Graphics_Color_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.WithBackgroundColor%60%601(%60%600%2CMicrosoft.Maui.Graphics.Color)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs/#L1438">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_WithBackgroundColor_" data-uid="DrawnUi.Draw.FluentExtensions.WithBackgroundColor*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_WithBackgroundColor__1___0_Microsoft_Maui_Graphics_Color_" data-uid="DrawnUi.Draw.FluentExtensions.WithBackgroundColor``1(``0,Microsoft.Maui.Graphics.Color)">WithBackgroundColor&lt;T&gt;(T, Color)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Sets the background color for the control</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T WithBackgroundColor&lt;T&gt;(this T view, Color color) where T : SkiaControl</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">view</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control to set background color for</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.color">Color</a></td>
        <td><span class="parametername">color</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The background color</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control for chaining</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Type of SkiaControl</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_WithBottom_Microsoft_Maui_Thickness_System_Double_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.WithBottom(Microsoft.Maui.Thickness%2CSystem.Double)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Internals/Extensions/FluentExtensions.Maui.cs/#L228">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_WithBottom_" data-uid="DrawnUi.Draw.FluentExtensions.WithBottom*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_WithBottom_Microsoft_Maui_Thickness_System_Double_" data-uid="DrawnUi.Draw.FluentExtensions.WithBottom(Microsoft.Maui.Thickness,System.Double)">WithBottom(Thickness, double)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Thickness WithBottom(this Thickness existing, double value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.thickness">Thickness</a></td>
        <td><span class="parametername">existing</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.thickness">Thickness</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_WithCache__1___0_DrawnUi_Draw_SkiaCacheType_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.WithCache%60%601(%60%600%2CDrawnUi.Draw.SkiaCacheType)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs/#L1425">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_WithCache_" data-uid="DrawnUi.Draw.FluentExtensions.WithCache*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_WithCache__1___0_DrawnUi_Draw_SkiaCacheType_" data-uid="DrawnUi.Draw.FluentExtensions.WithCache``1(``0,DrawnUi.Draw.SkiaCacheType)">WithCache&lt;T&gt;(T, SkiaCacheType)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Sets the cache type for the control</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T WithCache&lt;T&gt;(this T view, SkiaCacheType cacheType) where T : SkiaControl</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">view</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control to set cache for</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="DrawnUi.Draw.SkiaCacheType.html">SkiaCacheType</a></td>
        <td><span class="parametername">cacheType</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The cache type</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control for chaining</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Type of SkiaControl</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_WithChildren__1___0_DrawnUi_Draw_SkiaControl___.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.WithChildren%60%601(%60%600%2CDrawnUi.Draw.SkiaControl%5B%5D)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs/#L1131">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_WithChildren_" data-uid="DrawnUi.Draw.FluentExtensions.WithChildren*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_WithChildren__1___0_DrawnUi_Draw_SkiaControl___" data-uid="DrawnUi.Draw.FluentExtensions.WithChildren``1(``0,DrawnUi.Draw.SkiaControl[])">WithChildren&lt;T&gt;(T, params SkiaControl[])</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Adds multiple child controls to a layout</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T WithChildren&lt;T&gt;(this T view, params SkiaControl[] children) where T : SkiaLayout</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">view</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The layout to add children to</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a>[]</td>
        <td><span class="parametername">children</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The children to add</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The layout for chaining</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Type of SkiaLayout</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_WithColumnDefinitions_DrawnUi_Draw_SkiaLayout_System_String_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.WithColumnDefinitions(DrawnUi.Draw.SkiaLayout%2CSystem.String)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Internals/Extensions/FluentExtensions.Maui.cs/#L79">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_WithColumnDefinitions_" data-uid="DrawnUi.Draw.FluentExtensions.WithColumnDefinitions*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_WithColumnDefinitions_DrawnUi_Draw_SkiaLayout_System_String_" data-uid="DrawnUi.Draw.FluentExtensions.WithColumnDefinitions(DrawnUi.Draw.SkiaLayout,System.String)">WithColumnDefinitions(SkiaLayout, string)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Parses a string representation of column definitions and sets them on the grid</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static SkiaLayout WithColumnDefinitions(this SkiaLayout grid, string columnDefinitions)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="DrawnUi.Draw.SkiaLayout.html">SkiaLayout</a></td>
        <td><span class="parametername">grid</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The grid to set column definitions for</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></td>
        <td><span class="parametername">columnDefinitions</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">String in format like &quot;Auto,<em sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">,2</em>,100&quot;</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="DrawnUi.Draw.SkiaLayout.html">SkiaLayout</a></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The grid for chaining</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="exceptions">Exceptions</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Condition</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.invalidoperationexception">InvalidOperationException</a></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Thrown if conversion fails</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_WithColumnSpan__1___0_System_Int32_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.WithColumnSpan%60%601(%60%600%2CSystem.Int32)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Internals/Extensions/FluentExtensions.Maui.cs/#L66">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_WithColumnSpan_" data-uid="DrawnUi.Draw.FluentExtensions.WithColumnSpan*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_WithColumnSpan__1___0_System_Int32_" data-uid="DrawnUi.Draw.FluentExtensions.WithColumnSpan``1(``0,System.Int32)">WithColumnSpan&lt;T&gt;(T, int)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Sets the Grid.ColumnSpan attached property for the control</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T WithColumnSpan&lt;T&gt;(this T view, int columnSpan) where T : SkiaControl</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">view</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control to set the column span for</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></td>
        <td><span class="parametername">columnSpan</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The number of columns to span</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control for chaining</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Type of SkiaControl</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_WithColumn__1___0_System_Int32_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.WithColumn%60%601(%60%600%2CSystem.Int32)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Internals/Extensions/FluentExtensions.Maui.cs/#L40">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_WithColumn_" data-uid="DrawnUi.Draw.FluentExtensions.WithColumn*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_WithColumn__1___0_System_Int32_" data-uid="DrawnUi.Draw.FluentExtensions.WithColumn``1(``0,System.Int32)">WithColumn&lt;T&gt;(T, int)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Sets the Grid.Column attached property for the control</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T WithColumn&lt;T&gt;(this T view, int column) where T : SkiaControl</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">view</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control to set the column for</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></td>
        <td><span class="parametername">column</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The column index</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control for chaining</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Type of SkiaControl</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_WithContent__1___0_DrawnUi_Draw_SkiaControl_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.WithContent%60%601(%60%600%2CDrawnUi.Draw.SkiaControl)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs/#L1148">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_WithContent_" data-uid="DrawnUi.Draw.FluentExtensions.WithContent*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_WithContent__1___0_DrawnUi_Draw_SkiaControl_" data-uid="DrawnUi.Draw.FluentExtensions.WithContent``1(``0,DrawnUi.Draw.SkiaControl)">WithContent&lt;T&gt;(T, SkiaControl)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Sets the content of a container that implements IWithContent</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T WithContent&lt;T&gt;(this T view, SkiaControl child) where T : IWithContent</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">view</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The container</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></td>
        <td><span class="parametername">child</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The content to set</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The container for chaining</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Type implementing IWithContent</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_WithFontSize__1___0_System_Double_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.WithFontSize%60%601(%60%600%2CSystem.Double)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs/#L1567">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_WithFontSize_" data-uid="DrawnUi.Draw.FluentExtensions.WithFontSize*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_WithFontSize__1___0_System_Double_" data-uid="DrawnUi.Draw.FluentExtensions.WithFontSize``1(``0,System.Double)">WithFontSize&lt;T&gt;(T, double)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Sets the font size for SkiaLabel</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T WithFontSize&lt;T&gt;(this T label, double fontSize) where T : SkiaLabel</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">label</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The label to set font size for</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></td>
        <td><span class="parametername">fontSize</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The font size</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The label for chaining</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Type of SkiaLabel</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_WithGestures__1___0_System_Func___0_DrawnUi_Draw_SkiaGesturesParameters_DrawnUi_Draw_GestureEventProcessingInfo_DrawnUi_Draw_ISkiaGestureListener__.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.WithGestures%60%601(%60%600%2CSystem.Func%7B%60%600%2CDrawnUi.Draw.SkiaGesturesParameters%2CDrawnUi.Draw.GestureEventProcessingInfo%2CDrawnUi.Draw.ISkiaGestureListener%7D)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs/#L71">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_WithGestures_" data-uid="DrawnUi.Draw.FluentExtensions.WithGestures*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_WithGestures__1___0_System_Func___0_DrawnUi_Draw_SkiaGesturesParameters_DrawnUi_Draw_GestureEventProcessingInfo_DrawnUi_Draw_ISkiaGestureListener__" data-uid="DrawnUi.Draw.FluentExtensions.WithGestures``1(``0,System.Func{``0,DrawnUi.Draw.SkiaGesturesParameters,DrawnUi.Draw.GestureEventProcessingInfo,DrawnUi.Draw.ISkiaGestureListener})">WithGestures&lt;T&gt;(T, Func&lt;T, SkiaGesturesParameters, GestureEventProcessingInfo, ISkiaGestureListener&gt;)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Attaches a gesture handler to a SkiaLayout, allowing custom gesture processing.
You must return this control if you consumed a gesture, return null if not.
The UP gesture should be marked as consumed ONLY for specific scenarios, return null for it if unsure.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T WithGestures&lt;T&gt;(this T view, Func&lt;T, SkiaGesturesParameters, GestureEventProcessingInfo, ISkiaGestureListener&gt; func) where T : SkiaLayout</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">view</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The layout to attach gestures to</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.func-4">Func</a>&lt;T, <a class="xref" href="DrawnUi.Draw.SkiaGesturesParameters.html">SkiaGesturesParameters</a>, <a class="xref" href="DrawnUi.Draw.GestureEventProcessingInfo.html">GestureEventProcessingInfo</a>, <a class="xref" href="DrawnUi.Draw.ISkiaGestureListener.html">ISkiaGestureListener</a>&gt;</td>
        <td><span class="parametername">func</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">A function that returns a gesture listener for the layout</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The layout for chaining</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Type of SkiaLayout</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_WithHeightRequest__1___0_System_Double_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.WithHeightRequest%60%601(%60%600%2CSystem.Double)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs/#L1477">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_WithHeightRequest_" data-uid="DrawnUi.Draw.FluentExtensions.WithHeightRequest*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_WithHeightRequest__1___0_System_Double_" data-uid="DrawnUi.Draw.FluentExtensions.WithHeightRequest``1(``0,System.Double)">WithHeightRequest&lt;T&gt;(T, double)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Sets the height request for the control</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T WithHeightRequest&lt;T&gt;(this T view, double height) where T : SkiaControl</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">view</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control to set height for</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></td>
        <td><span class="parametername">height</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The height request</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control for chaining</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Type of SkiaControl</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_WithHorizontalOptions__1___0_Microsoft_Maui_Controls_LayoutOptions_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.WithHorizontalOptions%60%601(%60%600%2CMicrosoft.Maui.Controls.LayoutOptions)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs/#L1451">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_WithHorizontalOptions_" data-uid="DrawnUi.Draw.FluentExtensions.WithHorizontalOptions*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_WithHorizontalOptions__1___0_Microsoft_Maui_Controls_LayoutOptions_" data-uid="DrawnUi.Draw.FluentExtensions.WithHorizontalOptions``1(``0,Microsoft.Maui.Controls.LayoutOptions)">WithHorizontalOptions&lt;T&gt;(T, LayoutOptions)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Sets the horizontal options for the control</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T WithHorizontalOptions&lt;T&gt;(this T view, LayoutOptions options) where T : SkiaControl</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">view</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control to set horizontal options for</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.layoutoptions">LayoutOptions</a></td>
        <td><span class="parametername">options</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The horizontal layout options</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control for chaining</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Type of SkiaControl</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_WithHorizontalTextAlignment__1___0_DrawnUi_Draw_DrawTextAlignment_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.WithHorizontalTextAlignment%60%601(%60%600%2CDrawnUi.Draw.DrawTextAlignment)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs/#L1593">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_WithHorizontalTextAlignment_" data-uid="DrawnUi.Draw.FluentExtensions.WithHorizontalTextAlignment*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_WithHorizontalTextAlignment__1___0_DrawnUi_Draw_DrawTextAlignment_" data-uid="DrawnUi.Draw.FluentExtensions.WithHorizontalTextAlignment``1(``0,DrawnUi.Draw.DrawTextAlignment)">WithHorizontalTextAlignment&lt;T&gt;(T, DrawTextAlignment)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Sets the horizontal text alignment for SkiaLabel</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T WithHorizontalTextAlignment&lt;T&gt;(this T label, DrawTextAlignment alignment) where T : SkiaLabel</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">label</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The label to set alignment for</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="DrawnUi.Draw.DrawTextAlignment.html">DrawTextAlignment</a></td>
        <td><span class="parametername">alignment</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The horizontal text alignment</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The label for chaining</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Type of SkiaLabel</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_WithLeft_Microsoft_Maui_Thickness_System_Double_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.WithLeft(Microsoft.Maui.Thickness%2CSystem.Double)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Internals/Extensions/FluentExtensions.Maui.cs/#L238">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_WithLeft_" data-uid="DrawnUi.Draw.FluentExtensions.WithLeft*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_WithLeft_Microsoft_Maui_Thickness_System_Double_" data-uid="DrawnUi.Draw.FluentExtensions.WithLeft(Microsoft.Maui.Thickness,System.Double)">WithLeft(Thickness, double)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Thickness WithLeft(this Thickness existing, double value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.thickness">Thickness</a></td>
        <td><span class="parametername">existing</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.thickness">Thickness</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_WithMargin__1___0_Microsoft_Maui_Thickness_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.WithMargin%60%601(%60%600%2CMicrosoft.Maui.Thickness)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs/#L1503">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_WithMargin_" data-uid="DrawnUi.Draw.FluentExtensions.WithMargin*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_WithMargin__1___0_Microsoft_Maui_Thickness_" data-uid="DrawnUi.Draw.FluentExtensions.WithMargin``1(``0,Microsoft.Maui.Thickness)">WithMargin&lt;T&gt;(T, Thickness)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Sets the margin for the control</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T WithMargin&lt;T&gt;(this T view, Thickness margin) where T : SkiaControl</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">view</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control to set margin for</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.thickness">Thickness</a></td>
        <td><span class="parametername">margin</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The margin thickness</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control for chaining</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Type of SkiaControl</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_WithPadding__1___0_System_Double_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.WithPadding%60%601(%60%600%2CSystem.Double)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs/#L1364">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_WithPadding_" data-uid="DrawnUi.Draw.FluentExtensions.WithPadding*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_WithPadding__1___0_System_Double_" data-uid="DrawnUi.Draw.FluentExtensions.WithPadding``1(``0,System.Double)">WithPadding&lt;T&gt;(T, double)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Sets the padding for a layout control</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T WithPadding&lt;T&gt;(this T view, double uniformPadding) where T : SkiaLayout</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">view</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The layout to set padding for</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></td>
        <td><span class="parametername">uniformPadding</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The uniform padding to apply to all sides</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The layout for chaining</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Type of SkiaLayout</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_WithPadding__1___0_System_Double_System_Double_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.WithPadding%60%601(%60%600%2CSystem.Double%2CSystem.Double)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs/#L1378">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_WithPadding_" data-uid="DrawnUi.Draw.FluentExtensions.WithPadding*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_WithPadding__1___0_System_Double_System_Double_" data-uid="DrawnUi.Draw.FluentExtensions.WithPadding``1(``0,System.Double,System.Double)">WithPadding&lt;T&gt;(T, double, double)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Sets the padding for a layout control</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T WithPadding&lt;T&gt;(this T view, double horizontal, double vertical) where T : SkiaLayout</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">view</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The layout to set padding for</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></td>
        <td><span class="parametername">horizontal</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The left and right padding</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></td>
        <td><span class="parametername">vertical</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The top and bottom padding</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The layout for chaining</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Type of SkiaLayout</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_WithPadding__1___0_System_Double_System_Double_System_Double_System_Double_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.WithPadding%60%601(%60%600%2CSystem.Double%2CSystem.Double%2CSystem.Double%2CSystem.Double)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs/#L1394">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_WithPadding_" data-uid="DrawnUi.Draw.FluentExtensions.WithPadding*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_WithPadding__1___0_System_Double_System_Double_System_Double_System_Double_" data-uid="DrawnUi.Draw.FluentExtensions.WithPadding``1(``0,System.Double,System.Double,System.Double,System.Double)">WithPadding&lt;T&gt;(T, double, double, double, double)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Sets the padding for a layout control</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T WithPadding&lt;T&gt;(this T view, double left, double top, double right, double bottom) where T : SkiaLayout</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">view</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The layout to set padding for</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></td>
        <td><span class="parametername">left</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The left padding</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></td>
        <td><span class="parametername">top</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The top padding</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></td>
        <td><span class="parametername">right</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The right padding</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></td>
        <td><span class="parametername">bottom</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The bottom padding</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The layout for chaining</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Type of SkiaLayout</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_WithParent__1___0_DrawnUi_Draw_IDrawnBase_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.WithParent%60%601(%60%600%2CDrawnUi.Draw.IDrawnBase)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs/#L1161">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_WithParent_" data-uid="DrawnUi.Draw.FluentExtensions.WithParent*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_WithParent__1___0_DrawnUi_Draw_IDrawnBase_" data-uid="DrawnUi.Draw.FluentExtensions.WithParent``1(``0,DrawnUi.Draw.IDrawnBase)">WithParent&lt;T&gt;(T, IDrawnBase)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Adds the control to a parent and returns the control for further chaining</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T WithParent&lt;T&gt;(this T view, IDrawnBase parent) where T : SkiaControl</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">view</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control to add</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="DrawnUi.Draw.IDrawnBase.html">IDrawnBase</a></td>
        <td><span class="parametername">parent</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The parent to add the control to</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control for chaining</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Type of SkiaControl</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_WithRight_Microsoft_Maui_Thickness_System_Double_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.WithRight(Microsoft.Maui.Thickness%2CSystem.Double)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Internals/Extensions/FluentExtensions.Maui.cs/#L248">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_WithRight_" data-uid="DrawnUi.Draw.FluentExtensions.WithRight*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_WithRight_Microsoft_Maui_Thickness_System_Double_" data-uid="DrawnUi.Draw.FluentExtensions.WithRight(Microsoft.Maui.Thickness,System.Double)">WithRight(Thickness, double)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Thickness WithRight(this Thickness existing, double value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.thickness">Thickness</a></td>
        <td><span class="parametername">existing</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.thickness">Thickness</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_WithRowDefinitions_DrawnUi_Draw_SkiaLayout_System_String_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.WithRowDefinitions(DrawnUi.Draw.SkiaLayout%2CSystem.String)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Internals/Extensions/FluentExtensions.Maui.cs/#L101">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_WithRowDefinitions_" data-uid="DrawnUi.Draw.FluentExtensions.WithRowDefinitions*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_WithRowDefinitions_DrawnUi_Draw_SkiaLayout_System_String_" data-uid="DrawnUi.Draw.FluentExtensions.WithRowDefinitions(DrawnUi.Draw.SkiaLayout,System.String)">WithRowDefinitions(SkiaLayout, string)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Parses a string representation of row definitions and sets them on the grid</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static SkiaLayout WithRowDefinitions(this SkiaLayout grid, string definitions)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="DrawnUi.Draw.SkiaLayout.html">SkiaLayout</a></td>
        <td><span class="parametername">grid</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The grid to set row definitions for</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></td>
        <td><span class="parametername">definitions</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">String in format like &quot;Auto,<em sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">,2</em>,100&quot;</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="DrawnUi.Draw.SkiaLayout.html">SkiaLayout</a></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The grid for chaining</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="exceptions">Exceptions</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Condition</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.invalidoperationexception">InvalidOperationException</a></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Thrown if conversion fails</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_WithRowSpan__1___0_System_Int32_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.WithRowSpan%60%601(%60%600%2CSystem.Int32)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Internals/Extensions/FluentExtensions.Maui.cs/#L53">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_WithRowSpan_" data-uid="DrawnUi.Draw.FluentExtensions.WithRowSpan*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_WithRowSpan__1___0_System_Int32_" data-uid="DrawnUi.Draw.FluentExtensions.WithRowSpan``1(``0,System.Int32)">WithRowSpan&lt;T&gt;(T, int)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Sets the Grid.RowSpan attached property for the control</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T WithRowSpan&lt;T&gt;(this T view, int rowSpan) where T : SkiaControl</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">view</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control to set the row span for</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></td>
        <td><span class="parametername">rowSpan</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The number of rows to span</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control for chaining</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Type of SkiaControl</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_WithRow__1___0_System_Int32_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.WithRow%60%601(%60%600%2CSystem.Int32)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Internals/Extensions/FluentExtensions.Maui.cs/#L27">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_WithRow_" data-uid="DrawnUi.Draw.FluentExtensions.WithRow*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_WithRow__1___0_System_Int32_" data-uid="DrawnUi.Draw.FluentExtensions.WithRow``1(``0,System.Int32)">WithRow&lt;T&gt;(T, int)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Sets the Grid.Row attached property for the control</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T WithRow&lt;T&gt;(this T view, int row) where T : SkiaControl</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">view</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control to set the row for</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></td>
        <td><span class="parametername">row</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The row index</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control for chaining</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Type of SkiaControl</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_WithShapeType__1___0_DrawnUi_Draw_ShapeType_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.WithShapeType%60%601(%60%600%2CDrawnUi.Draw.ShapeType)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs/#L1520">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_WithShapeType_" data-uid="DrawnUi.Draw.FluentExtensions.WithShapeType*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_WithShapeType__1___0_DrawnUi_Draw_ShapeType_" data-uid="DrawnUi.Draw.FluentExtensions.WithShapeType``1(``0,DrawnUi.Draw.ShapeType)">WithShapeType&lt;T&gt;(T, ShapeType)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Sets the shape type for SkiaShape</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T WithShapeType&lt;T&gt;(this T shape, ShapeType shapeType) where T : SkiaShape</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">shape</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The shape to set type for</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="DrawnUi.Draw.ShapeType.html">ShapeType</a></td>
        <td><span class="parametername">shapeType</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The shape type</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The shape for chaining</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Type of SkiaShape</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_WithTag__1___0_System_String_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.WithTag%60%601(%60%600%2CSystem.String)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs/#L1408">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_WithTag_" data-uid="DrawnUi.Draw.FluentExtensions.WithTag*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_WithTag__1___0_System_String_" data-uid="DrawnUi.Draw.FluentExtensions.WithTag``1(``0,System.String)">WithTag&lt;T&gt;(T, string)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Sets the Tag property for the control</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T WithTag&lt;T&gt;(this T view, string tag) where T : SkiaControl</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">view</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control to set the Tag for</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></td>
        <td><span class="parametername">tag</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The tag value</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control for chaining</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Type of SkiaControl</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_WithTextColor__1___0_Microsoft_Maui_Graphics_Color_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.WithTextColor%60%601(%60%600%2CMicrosoft.Maui.Graphics.Color)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs/#L1580">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_WithTextColor_" data-uid="DrawnUi.Draw.FluentExtensions.WithTextColor*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_WithTextColor__1___0_Microsoft_Maui_Graphics_Color_" data-uid="DrawnUi.Draw.FluentExtensions.WithTextColor``1(``0,Microsoft.Maui.Graphics.Color)">WithTextColor&lt;T&gt;(T, Color)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Sets the text color for SkiaLabel</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T WithTextColor&lt;T&gt;(this T label, Color color) where T : SkiaLabel</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">label</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The label to set text color for</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.color">Color</a></td>
        <td><span class="parametername">color</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The text color</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The label for chaining</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Type of SkiaLabel</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_WithTop_Microsoft_Maui_Thickness_System_Double_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.WithTop(Microsoft.Maui.Thickness%2CSystem.Double)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Internals/Extensions/FluentExtensions.Maui.cs/#L218">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_WithTop_" data-uid="DrawnUi.Draw.FluentExtensions.WithTop*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_WithTop_Microsoft_Maui_Thickness_System_Double_" data-uid="DrawnUi.Draw.FluentExtensions.WithTop(Microsoft.Maui.Thickness,System.Double)">WithTop(Thickness, double)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Thickness WithTop(this Thickness existing, double value)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.thickness">Thickness</a></td>
        <td><span class="parametername">existing</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></td>
        <td><span class="parametername">value</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.thickness">Thickness</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_WithVerticalOptions__1___0_Microsoft_Maui_Controls_LayoutOptions_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.WithVerticalOptions%60%601(%60%600%2CMicrosoft.Maui.Controls.LayoutOptions)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs/#L1464">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_WithVerticalOptions_" data-uid="DrawnUi.Draw.FluentExtensions.WithVerticalOptions*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_WithVerticalOptions__1___0_Microsoft_Maui_Controls_LayoutOptions_" data-uid="DrawnUi.Draw.FluentExtensions.WithVerticalOptions``1(``0,Microsoft.Maui.Controls.LayoutOptions)">WithVerticalOptions&lt;T&gt;(T, LayoutOptions)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Sets the vertical options for the control</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T WithVerticalOptions&lt;T&gt;(this T view, LayoutOptions options) where T : SkiaControl</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">view</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control to set vertical options for</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.layoutoptions">LayoutOptions</a></td>
        <td><span class="parametername">options</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The vertical layout options</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control for chaining</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Type of SkiaControl</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions_WithWidthRequest__1___0_System_Double_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions.WithWidthRequest%60%601(%60%600%2CSystem.Double)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs/#L1490">View Source</a>
  </span>
  <a id="DrawnUi_Draw_FluentExtensions_WithWidthRequest_" data-uid="DrawnUi.Draw.FluentExtensions.WithWidthRequest*"></a>
  <h4 id="DrawnUi_Draw_FluentExtensions_WithWidthRequest__1___0_System_Double_" data-uid="DrawnUi.Draw.FluentExtensions.WithWidthRequest``1(``0,System.Double)">WithWidthRequest&lt;T&gt;(T, double)</h4>
  <div class="markdown level1 summary"><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Sets the width request for the control</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T WithWidthRequest&lt;T&gt;(this T view, double width) where T : SkiaControl</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><span class="parametername">view</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control to set width for</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></td>
        <td><span class="parametername">width</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The width request</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">The control for chaining</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td><p sourcefile="api/DrawnUi.Draw.FluentExtensions.yml" sourcestartlinenumber="1">Type of SkiaControl</p>
</td>
      </tr>
    </tbody>
  </table>

</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                  <li>
                    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_FluentExtensions.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.FluentExtensions%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A" class="contribution-link">Edit this page</a>
                  </li>
                  <li>
                    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs/#L11" class="contribution-link">View Source</a>
                  </li>
                </ul>
              </div>
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In this article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      
      <span>Generated by <strong>DocFX</strong></span>
          </div>
        </div>
      </footer>
    </div>

    <script type="text/javascript" src="../styles/docfx.vendor.min.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
