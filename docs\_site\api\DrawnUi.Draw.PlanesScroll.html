<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
      <title>Class PlanesScroll | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width">
      <meta name="title" content="Class PlanesScroll | DrawnUi Documentation ">
    
    <meta name="description" content="Provides the ability to create/draw views directly while scrolling. Content will be generated dynamically, instead of the usual way. This control main logic is inside PaintOnPlane override, also it hacks content to work without a real Content. You have to override GetMeasuredView to provide your views to be drawn upon passed index. TODO: for horizonal">
      <link rel="shortcut icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../styles/docfx.vendor.min.css">
      <link rel="stylesheet" href="../styles/docfx.css">
      <link rel="stylesheet" href="../styles/main.css">
      <meta property="docfx:navrel" content="../toc.html">
      <meta property="docfx:tocrel" content="toc.html">
    
    <meta property="docfx:rel" content="../">
    
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first=First data-prev=Previous data-next=Next data-last=Last></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">

        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="DrawnUi.Draw.PlanesScroll">



  <h1 id="DrawnUi_Draw_PlanesScroll" data-uid="DrawnUi.Draw.PlanesScroll" class="text-break">Class PlanesScroll</h1>
  <div class="markdown level0 summary"><p sourcefile="api/DrawnUi.Draw.PlanesScroll.yml" sourcestartlinenumber="1">Provides the ability to create/draw views directly while scrolling.
Content will be generated dynamically, instead of the usual way.
This control main logic is inside PaintOnPlane override, also it hacks content to work without a real Content.
You have to override <code sourcefile="api/DrawnUi.Draw.PlanesScroll.yml" sourcestartlinenumber="4">GetMeasuredView</code> to provide your views to be drawn upon passed index.
TODO: for horizonal</p>
</div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></div>
    <div class="level1"><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject">BindableObject</a></div>
    <div class="level2"><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element">Element</a></div>
    <div class="level3"><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.styleableelement">StyleableElement</a></div>
    <div class="level4"><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.navigableelement">NavigableElement</a></div>
    <div class="level5"><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement">VisualElement</a></div>
    <div class="level6"><a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></div>
    <div class="level7"><a class="xref" href="DrawnUi.Draw.SkiaScroll.html">SkiaScroll</a></div>
    <div class="level8"><a class="xref" href="DrawnUi.Draw.VirtualScroll.html">VirtualScroll</a></div>
    <div class="level9"><span class="xref">PlanesScroll</span></div>
  </div>
  <div class="implements">
    <h5>Implements</h5>
    <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.componentmodel.inotifypropertychanged">INotifyPropertyChanged</a></div>
    <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ieffectcontrolprovider">IEffectControlProvider</a></div>
    <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.itooltipelement">IToolTipElement</a></div>
    <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.icontextflyoutelement">IContextFlyoutElement</a></div>
    <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ianimatable">IAnimatable</a></div>
    <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ivisualelementcontroller">IVisualElementController</a></div>
    <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ielementcontroller">IElementController</a></div>
    <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.hotreload.ihotreloadableview">IHotReloadableView</a></div>
    <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.ireplaceableview">IReplaceableView</a></div>
    <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.iview">IView</a></div>
    <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.ielement">IElement</a></div>
    <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.itransform">ITransform</a></div>
    <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.hotreload.ireloadhandler">IReloadHandler</a></div>
    <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.ivisualtreeelement">IVisualTreeElement</a></div>
    <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.icontainer">IContainer</a></div>
    <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.ilist-1">IList</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.iview">IView</a>&gt;</div>
    <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.icollection-1">ICollection</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.iview">IView</a>&gt;</div>
    <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1">IEnumerable</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.iview">IView</a>&gt;</div>
    <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.ienumerable">IEnumerable</a></div>
    <div><a class="xref" href="DrawnUi.Draw.IHasAfterEffects.html">IHasAfterEffects</a></div>
    <div><a class="xref" href="DrawnUi.Draw.ISkiaControl.html">ISkiaControl</a></div>
    <div><a class="xref" href="DrawnUi.Draw.IDrawnBase.html">IDrawnBase</a></div>
    <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.idisposable">IDisposable</a></div>
    <div><a class="xref" href="DrawnUi.Draw.ICanBeUpdatedWithContext.html">ICanBeUpdatedWithContext</a></div>
    <div><a class="xref" href="DrawnUi.Draw.ICanBeUpdated.html">ICanBeUpdated</a></div>
    <div><a class="xref" href="DrawnUi.Draw.ISkiaGestureListener.html">ISkiaGestureListener</a></div>
    <div><a class="xref" href="DrawnUi.Draw.IDefinesViewport.html">IDefinesViewport</a></div>
    <div><a class="xref" href="DrawnUi.Draw.IWithContent.html">IWithContent</a></div>
  </div>
  <div class="inheritedMembers">
    <h5>Inherited Members</h5>
    <div>
      <a class="xref" href="DrawnUi.Draw.VirtualScroll.html#DrawnUi_Draw_VirtualScroll_IsContentActive">VirtualScroll.IsContentActive</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.VirtualScroll.html#DrawnUi_Draw_VirtualScroll_SetDetectIndexChildPoint_DrawnUi_Draw_RelativePositionType_">VirtualScroll.SetDetectIndexChildPoint(RelativePositionType)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.VirtualScroll.html#DrawnUi_Draw_VirtualScroll_SetContentSize">VirtualScroll.SetContentSize()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.VirtualScroll.html#DrawnUi_Draw_VirtualScroll_GetMeasuredView_System_Int32_SkiaSharp_SKRect_System_Single_">VirtualScroll.GetMeasuredView(int, SKRect, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.VirtualScroll.html#DrawnUi_Draw_VirtualScroll_PositionViewport_SkiaSharp_SKRect_SkiaSharp_SKPoint_System_Single_System_Single_System_Boolean_">VirtualScroll.PositionViewport(SKRect, SKPoint, float, float, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ThesholdSwipeOnUp">SkiaScroll.ThesholdSwipeOnUp</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ScrollVelocityThreshold">SkiaScroll.ScrollVelocityThreshold</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_SystemAnimationTimeSecs">SkiaScroll.SystemAnimationTimeSecs</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_OnWillDisposeWithChildren">SkiaScroll.OnWillDisposeWithChildren()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_InteractionState">SkiaScroll.InteractionState</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_UpdateVisibleIndex">SkiaScroll.UpdateVisibleIndex()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_HasContentToScroll">SkiaScroll.HasContentToScroll</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_HeaderStickyProperty">SkiaScroll.HeaderStickyProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_HeaderSticky">SkiaScroll.HeaderSticky</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ParallaxOverscrollEnabledProperty">SkiaScroll.ParallaxOverscrollEnabledProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ParallaxOverscrollEnabled">SkiaScroll.ParallaxOverscrollEnabled</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_HeaderBehindProperty">SkiaScroll.HeaderBehindProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_HeaderBehind">SkiaScroll.HeaderBehind</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ContentOffsetProperty">SkiaScroll.ContentOffsetProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ContentOffset">SkiaScroll.ContentOffset</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_HeaderProperty">SkiaScroll.HeaderProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_Header">SkiaScroll.Header</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_HeaderParallaxRatioProperty">SkiaScroll.HeaderParallaxRatioProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_HeaderParallaxRatio">SkiaScroll.HeaderParallaxRatio</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_FooterProperty">SkiaScroll.FooterProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_Footer">SkiaScroll.Footer</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_RefreshIndicatorProperty">SkiaScroll.RefreshIndicatorProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_RefreshIndicator">SkiaScroll.RefreshIndicator</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_InternalRefreshIndicator">SkiaScroll.InternalRefreshIndicator</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_OrderedScrollProperty">SkiaScroll.OrderedScrollProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_OrderedScroll">SkiaScroll.OrderedScroll</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_OrderedScrollIsAnimatedProperty">SkiaScroll.OrderedScrollIsAnimatedProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_OrderedScrollIsAnimated">SkiaScroll.OrderedScrollIsAnimated</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_RefreshEnabledProperty">SkiaScroll.RefreshEnabledProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_RefreshEnabled">SkiaScroll.RefreshEnabled</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_IsRefreshingProperty">SkiaScroll.IsRefreshingProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_IsRefreshing">SkiaScroll.IsRefreshing</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_RefreshCommandProperty">SkiaScroll.RefreshCommandProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_RefreshCommand">SkiaScroll.RefreshCommand</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_RefreshDistanceLimitProperty">SkiaScroll.RefreshDistanceLimitProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_RefreshDistanceLimit">SkiaScroll.RefreshDistanceLimit</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_RefreshShowDistanceProperty">SkiaScroll.RefreshShowDistanceProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_RefreshShowDistance">SkiaScroll.RefreshShowDistance</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ScrollingEasing">SkiaScroll.ScrollingEasing</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll__velocitySwipe">SkiaScroll._velocitySwipe</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll__velocitySwipeRatio">SkiaScroll._velocitySwipeRatio</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll__panningLastDelta">SkiaScroll._panningLastDelta</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll__panningCurrentOffsetPts">SkiaScroll._panningCurrentOffsetPts</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_PlayEdgeGlowAnimation_Microsoft_Maui_Graphics_Color_System_Double_System_Double_System_Boolean_">SkiaScroll.PlayEdgeGlowAnimation(Color, double, double, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_OverscrollDistance">SkiaScroll.OverscrollDistance</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ScrollLocked">SkiaScroll.ScrollLocked</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_VelocityTrackerPan">SkiaScroll.VelocityTrackerPan</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_VelocityTrackerScale">SkiaScroll.VelocityTrackerScale</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ContentOffsetBounds">SkiaScroll.ContentOffsetBounds</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ClampOffsetWithRubberBand_System_Single_System_Single_SkiaSharp_SKRect_">SkiaScroll.ClampOffsetWithRubberBand(float, float, SKRect)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_Elastic">SkiaScroll.Elastic</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ClampOffset_System_Single_System_Single_SkiaSharp_SKRect_System_Boolean_">SkiaScroll.ClampOffset(float, float, SKRect, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_RespondsToGesturesProperty">SkiaScroll.RespondsToGesturesProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_RespondsToGestures">SkiaScroll.RespondsToGestures</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_CanScrollUsingHeaderProperty">SkiaScroll.CanScrollUsingHeaderProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_CanScrollUsingHeader">SkiaScroll.CanScrollUsingHeader</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ContentGesturesHit">SkiaScroll.ContentGesturesHit</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_IsGestureForChild_DrawnUi_Draw_ISkiaGestureListener_System_Single_System_Single_">SkiaScroll.IsGestureForChild(ISkiaGestureListener, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ChildWasPanning">SkiaScroll.ChildWasPanning</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ChildWasTapped">SkiaScroll.ChildWasTapped</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_SwipeVelocityAccumulator">SkiaScroll.SwipeVelocityAccumulator</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_UsesRenderingTree">SkiaScroll.UsesRenderingTree</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll__vectorAnimatorBounceX">SkiaScroll._vectorAnimatorBounceX</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll__vectorAnimatorBounceY">SkiaScroll._vectorAnimatorBounceY</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll__animatorFlingX">SkiaScroll._animatorFlingX</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll__animatorFlingY">SkiaScroll._animatorFlingY</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll__scrollerX">SkiaScroll._scrollerX</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll__scrollerY">SkiaScroll._scrollerY</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll__scrollMinX">SkiaScroll._scrollMinX</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll__scrollMinY">SkiaScroll._scrollMinY</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll__scrollMaxX">SkiaScroll._scrollMaxX</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll__scrollMaxY">SkiaScroll._scrollMaxY</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_StopScrolling">SkiaScroll.StopScrolling()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_BouncesProperty">SkiaScroll.BouncesProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_Bounces">SkiaScroll.Bounces</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_RubberDampingProperty">SkiaScroll.RubberDampingProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_RubberDamping">SkiaScroll.RubberDamping</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_RubberEffectProperty">SkiaScroll.RubberEffectProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_RubberEffect">SkiaScroll.RubberEffect</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_SnapBouncingIfVelocityLessThan">SkiaScroll.SnapBouncingIfVelocityLessThan</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_SnapBouncingIfVelocityLessThanProperty">SkiaScroll.SnapBouncingIfVelocityLessThanProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_AutoScrollingSpeedMsProperty">SkiaScroll.AutoScrollingSpeedMsProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_AutoScrollingSpeedMs">SkiaScroll.AutoScrollingSpeedMs</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_FrictionScrolled">SkiaScroll.FrictionScrolled</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_FrictionScrolledProperty">SkiaScroll.FrictionScrolledProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_IgnoreWrongDirectionProperty">SkiaScroll.IgnoreWrongDirectionProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_IgnoreWrongDirection">SkiaScroll.IgnoreWrongDirection</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ResetScrollPositionOnContentSizeChangedProperty">SkiaScroll.ResetScrollPositionOnContentSizeChangedProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ResetScrollPositionOnContentSizeChanged">SkiaScroll.ResetScrollPositionOnContentSizeChanged</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ChangeVelocityScrolled">SkiaScroll.ChangeVelocityScrolled</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ChangeVelocityScrolledProperty">SkiaScroll.ChangeVelocityScrolledProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_MaxVelocityProperty">SkiaScroll.MaxVelocityProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_MaxVelocity">SkiaScroll.MaxVelocity</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_MaxBounceVelocityProperty">SkiaScroll.MaxBounceVelocityProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_MaxBounceVelocity">SkiaScroll.MaxBounceVelocity</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ChangeDistancePanned">SkiaScroll.ChangeDistancePanned</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ChangeDistancePannedProperty">SkiaScroll.ChangeDistancePannedProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_CurrentIndex">SkiaScroll.CurrentIndex</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_IndexChanged">SkiaScroll.IndexChanged</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_CurrentIndexHit">SkiaScroll.CurrentIndexHit</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_DetectIndexChildIndexAt">SkiaScroll.DetectIndexChildIndexAt</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_CalculateVisibleIndex_DrawnUi_Draw_RelativePositionType_">SkiaScroll.CalculateVisibleIndex(RelativePositionType)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_GetItemIndex_DrawnUi_Draw_SkiaLayout_System_Single_System_Single_DrawnUi_Draw_RelativePositionType_">SkiaScroll.GetItemIndex(SkiaLayout, float, float, RelativePositionType)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ClampedOrderedScrollOffset_SkiaSharp_SKPoint_">SkiaScroll.ClampedOrderedScrollOffset(SKPoint)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_CalculateScrollOffsetForIndex_System_Int32_DrawnUi_Draw_RelativePositionType_">SkiaScroll.CalculateScrollOffsetForIndex(int, RelativePositionType)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_CheckNeedToSnap">SkiaScroll.CheckNeedToSnap()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_Snap_System_Single_">SkiaScroll.Snap(float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_SnapToChildrenProperty">SkiaScroll.SnapToChildrenProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_SnapToChildren">SkiaScroll.SnapToChildren</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_TrackIndexPositionProperty">SkiaScroll.TrackIndexPositionProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_TrackIndexPosition">SkiaScroll.TrackIndexPosition</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_TrackIndexPositionOffsetProperty">SkiaScroll.TrackIndexPositionOffsetProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_TrackIndexPositionOffset">SkiaScroll.TrackIndexPositionOffset</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_LoadMoreCommandProperty">SkiaScroll.LoadMoreCommandProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_LoadMoreCommand">SkiaScroll.LoadMoreCommand</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_LoadMoreOffsetProperty">SkiaScroll.LoadMoreOffsetProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_LoadMoreOffset">SkiaScroll.LoadMoreOffset</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_LastContentSizePixels">SkiaScroll.LastContentSizePixels</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_LastMeasuredSizePixels">SkiaScroll.LastMeasuredSizePixels</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ApplyContentSize">SkiaScroll.ApplyContentSize()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_PassGestureToChildren_DrawnUi_Draw_SkiaGesturesParameters_DrawnUi_Draw_GestureEventProcessingInfo_">SkiaScroll.PassGestureToChildren(SkiaGesturesParameters, GestureEventProcessingInfo)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_VelocityY">SkiaScroll.VelocityY</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_VelocityX">SkiaScroll.VelocityX</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_SetZoom_System_Double_">SkiaScroll.SetZoom(double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ZoomScaleInternal">SkiaScroll.ZoomScaleInternal</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_HeaderSize">SkiaScroll.HeaderSize</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_FooterSize">SkiaScroll.FooterSize</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_MeasureContent_System_Single_System_Single_System_Single_">SkiaScroll.MeasureContent(float, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_MeasureInternal_DrawnUi_Draw_MeasureRequest_">SkiaScroll.MeasureInternal(MeasureRequest)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_Viewport">SkiaScroll.Viewport</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_SetMeasured_System_Single_System_Single_System_Boolean_System_Boolean_System_Single_">SkiaScroll.SetMeasured(float, float, bool, bool, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_GetContentAvailableRect_SkiaSharp_SKRect_">SkiaScroll.GetContentAvailableRect(SKRect)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_InternalViewportOffset">SkiaScroll.InternalViewportOffset</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ContentViewport">SkiaScroll.ContentViewport</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_AdjustHeaderParallax">SkiaScroll.AdjustHeaderParallax()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_SendScrolled">SkiaScroll.SendScrolled()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_SendScrollingEnded">SkiaScroll.SendScrollingEnded()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_OnScrollCompleted">SkiaScroll.OnScrollCompleted()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_IsScrolling">SkiaScroll.IsScrolling</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_HideRefreshIndicator">SkiaScroll.HideRefreshIndicator()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_OnScrolled">SkiaScroll.OnScrolled()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ScrollingEnded">SkiaScroll.ScrollingEnded</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_Scrolled">SkiaScroll.Scrolled</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_UsingRefreshDistanceLimit">SkiaScroll.UsingRefreshDistanceLimit</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ShowRefreshIndicatorForced">SkiaScroll.ShowRefreshIndicatorForced()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ApplyScrollPositionToRefreshViewUnsafe">SkiaScroll.ApplyScrollPositionToRefreshViewUnsafe()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_CheckNeedRefresh">SkiaScroll.CheckNeedRefresh()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_SetIsRefreshing_System_Boolean_">SkiaScroll.SetIsRefreshing(bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_PaintViews_DrawnUi_Draw_DrawingContext_">SkiaScroll.PaintViews(DrawingContext)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_Paint_DrawnUi_Draw_DrawingContext_">SkiaScroll.Paint(DrawingContext)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_Draw_DrawnUi_Draw_DrawingContext_">SkiaScroll.Draw(DrawingContext)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_SetScrollOffset_SkiaSharp_SKRect_System_Single_System_Single_System_Single_System_Single_System_Boolean_">SkiaScroll.SetScrollOffset(SKRect, float, float, float, float, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ParallaxComputedValue">SkiaScroll.ParallaxComputedValue</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_DrawViews_DrawnUi_Draw_DrawingContext_">SkiaScroll.DrawViews(DrawingContext)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ContentAvailableSpace">SkiaScroll.ContentAvailableSpace</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ContentRectWithOffset">SkiaScroll.ContentRectWithOffset</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_Init">SkiaScroll.Init()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_SetChildren_System_Collections_Generic_IEnumerable_DrawnUi_Draw_SkiaControl__">SkiaScroll.SetChildren(IEnumerable&lt;SkiaControl&gt;)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ApplyBindingContext">SkiaScroll.ApplyBindingContext()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_SetContent_DrawnUi_Draw_SkiaControl_">SkiaScroll.SetContent(SkiaControl)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_SetHeader_DrawnUi_Draw_SkiaControl_">SkiaScroll.SetHeader(SkiaControl)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_SetFooter_DrawnUi_Draw_SkiaControl_">SkiaScroll.SetFooter(SkiaControl)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ScrollingSpeedMsProperty">SkiaScroll.ScrollingSpeedMsProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ScrollingSpeedMs">SkiaScroll.ScrollingSpeedMs</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ZoomLockedProperty">SkiaScroll.ZoomLockedProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ZoomLocked">SkiaScroll.ZoomLocked</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ZoomMinProperty">SkiaScroll.ZoomMinProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ZoomMin">SkiaScroll.ZoomMin</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ZoomMaxProperty">SkiaScroll.ZoomMaxProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ZoomMax">SkiaScroll.ZoomMax</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ViewportZoomProperty">SkiaScroll.ViewportZoomProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ViewportZoom">SkiaScroll.ViewportZoom</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_VelocityImageLoaderLockProperty">SkiaScroll.VelocityImageLoaderLockProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_VelocityImageLoaderLock">SkiaScroll.VelocityImageLoaderLock</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ContentProperty">SkiaScroll.ContentProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_Content">SkiaScroll.Content</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_OrientationProperty">SkiaScroll.OrientationProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_Orientation">SkiaScroll.Orientation</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ScrollTypeProperty">SkiaScroll.ScrollTypeProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ScrollType">SkiaScroll.ScrollType</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_VirtualisationProperty">SkiaScroll.VirtualisationProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_Virtualisation">SkiaScroll.Virtualisation</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_AdaptToKeyboardForProperty">SkiaScroll.AdaptToKeyboardForProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_AdaptToKeyboardFor">SkiaScroll.AdaptToKeyboardFor</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_AdaptToKeyboardSizeProperty">SkiaScroll.AdaptToKeyboardSizeProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_AdaptToKeyboardSize">SkiaScroll.AdaptToKeyboardSize</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_CalculateNeededScrollForKeyboard">SkiaScroll.CalculateNeededScrollForKeyboard()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_AdaptToKeyboard">SkiaScroll.AdaptToKeyboard()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_OnPropertyChanged_System_String_">SkiaScroll.OnPropertyChanged(string)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_InvalidateViewport">SkiaScroll.InvalidateViewport()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_WillClipBounds">SkiaScroll.WillClipBounds</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_OnDrawn_DrawnUi_Draw_DrawingContext_">SkiaScroll.OnDrawn(DrawingContext)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ReverseGestures">SkiaScroll.ReverseGestures</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_IsGestureForChild_DrawnUi_Draw_SkiaControl_DrawnUi_Draw_SkiaGesturesParameters_">SkiaScroll.IsGestureForChild(SkiaControl, SkiaGesturesParameters)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_IsGestureForChild_DrawnUi_Draw_SkiaControlWithRect_SkiaSharp_SKPoint_">SkiaScroll.IsGestureForChild(SkiaControlWithRect, SKPoint)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_IsGestureForChild_DrawnUi_Draw_ISkiaGestureListener_SkiaSharp_SKPoint_">SkiaScroll.IsGestureForChild(ISkiaGestureListener, SKPoint)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_LockGesturesUntilDown">SkiaScroll.LockGesturesUntilDown</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll__pannedOffset">SkiaScroll._pannedOffset</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll__pannedVelocity">SkiaScroll._pannedVelocity</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll__pannedVelocityRemaining">SkiaScroll._pannedVelocityRemaining</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_WasSwiping">SkiaScroll.WasSwiping</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_IsUserFocused">SkiaScroll.IsUserFocused</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_IsUserPanning">SkiaScroll.IsUserPanning</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_HadDown">SkiaScroll.HadDown</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ResetOverscroll">SkiaScroll.ResetOverscroll()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ResetPan">SkiaScroll.ResetPan()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_StopVelocityPanning">SkiaScroll.StopVelocityPanning()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ProcessGestures_DrawnUi_Draw_SkiaGesturesParameters_DrawnUi_Draw_GestureEventProcessingInfo_">SkiaScroll.ProcessGestures(SkiaGesturesParameters, GestureEventProcessingInfo)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_OnFocusChanged_System_Boolean_">SkiaScroll.OnFocusChanged(bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ApplyPannedOffsetWithVelocity_DrawnUi_Draw_SkiaDrawingContext_">SkiaScroll.ApplyPannedOffsetWithVelocity(SkiaDrawingContext)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_UpdateByChild_DrawnUi_Draw_SkiaControl_">SkiaScroll.UpdateByChild(SkiaControl)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_PlaneCurrent">SkiaScroll.PlaneCurrent</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_PlaneForward">SkiaScroll.PlaneForward</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_PlaneBackward">SkiaScroll.PlaneBackward</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll__planeWidth">SkiaScroll._planeWidth</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll__planeHeight">SkiaScroll._planeHeight</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll__planePrepareThreshold">SkiaScroll._planePrepareThreshold</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_GetOnScreenVisibleArea_DrawnUi_Draw_DrawingContext_System_Numerics_Vector2_">SkiaScroll.GetOnScreenVisibleArea(DrawingContext, Vector2)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_InitializePlanes">SkiaScroll.InitializePlanes()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll__planesInverted">SkiaScroll._planesInverted</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_SwapPlanes">SkiaScroll.SwapPlanes()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll__buildingPlaneB">SkiaScroll._buildingPlaneB</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll__buildingPlaneC">SkiaScroll._buildingPlaneC</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_TriggerPreparePlane_DrawnUi_Draw_DrawingContext_System_String_">SkiaScroll.TriggerPreparePlane(DrawingContext, string)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_GetPlaneById_System_String_">SkiaScroll.GetPlaneById(string)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_OnScrolledForPlanes">SkiaScroll.OnScrolledForPlanes()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_OrderToPreparePlaneForwardInBackground_DrawnUi_Draw_DrawingContext_">SkiaScroll.OrderToPreparePlaneForwardInBackground(DrawingContext)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_OrderToPreparePlaneBackwardInBackground_DrawnUi_Draw_DrawingContext_">SkiaScroll.OrderToPreparePlaneBackwardInBackground(DrawingContext)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_DrawVirtual_DrawnUi_Draw_DrawingContext_">SkiaScroll.DrawVirtual(DrawingContext)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ViewportOffsetY">SkiaScroll.ViewportOffsetY</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll__viewportOffsetY">SkiaScroll._viewportOffsetY</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ViewportOffsetX">SkiaScroll.ViewportOffsetX</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll__viewportOffsetX">SkiaScroll._viewportOffsetX</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_InitializeViewport_System_Single_">SkiaScroll.InitializeViewport(float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_IsViewportReady">SkiaScroll.IsViewportReady</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ScrollingDirection">SkiaScroll.ScrollingDirection</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_CheckAndSetIsStillAnimating">SkiaScroll.CheckAndSetIsStillAnimating()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_InitializeScroller_System_Single_">SkiaScroll.InitializeScroller(float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ScrollToX_System_Single_System_Boolean_">SkiaScroll.ScrollToX(float, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ScrollToY_System_Single_System_Boolean_">SkiaScroll.ScrollToY(float, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_OnScrollerStarted">SkiaScroll.OnScrollerStarted()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_OnScrollerUpdated">SkiaScroll.OnScrollerUpdated()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_BounceIfNeeded_DrawnUi_Draw_ScrollFlingAnimator_">SkiaScroll.BounceIfNeeded(ScrollFlingAnimator)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_OnScrollerStopped">SkiaScroll.OnScrollerStopped()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ExecuteDelayedScrollOrders">SkiaScroll.ExecuteDelayedScrollOrders()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_GetClosestSidePoint_SkiaSharp_SKPoint_SkiaSharp_SKRect_SkiaSharp_SKSize_">SkiaScroll.GetClosestSidePoint(SKPoint, SKRect, SKSize)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ClosestPoint_SkiaSharp_SKRect_SkiaSharp_SKPoint_">SkiaScroll.ClosestPoint(SKRect, SKPoint)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_OffsetOk_System_Numerics_Vector2_">SkiaScroll.OffsetOk(Vector2)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_OverScrolled">SkiaScroll.OverScrolled</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ptsContentWidth">SkiaScroll.ptsContentWidth</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ptsContentHeight">SkiaScroll.ptsContentHeight</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_GetContentOffsetBounds">SkiaScroll.GetContentOffsetBounds()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_CalculateOverscrollDistance_System_Single_System_Single_">SkiaScroll.CalculateOverscrollDistance(float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll__minVelocity">SkiaScroll._minVelocity</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_DecelerationRatio">SkiaScroll.DecelerationRatio</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_UpdateFriction">SkiaScroll.UpdateFriction()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_StartToFlingFrom_DrawnUi_Draw_ScrollFlingAnimator_System_Single_System_Single_">SkiaScroll.StartToFlingFrom(ScrollFlingAnimator, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_FlingFrom_DrawnUi_Draw_ScrollFlingAnimator_System_Single_System_Single_">SkiaScroll.FlingFrom(ScrollFlingAnimator, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_FlingToAuto_DrawnUi_Draw_ScrollFlingAnimator_System_Single_System_Single_System_Single_">SkiaScroll.FlingToAuto(ScrollFlingAnimator, float, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_FlingTo_DrawnUi_Draw_ScrollFlingAnimator_System_Single_System_Single_System_Single_">SkiaScroll.FlingTo(ScrollFlingAnimator, float, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_PrepareToFlingAfterInitialized_DrawnUi_Draw_ScrollFlingAnimator_">SkiaScroll.PrepareToFlingAfterInitialized(ScrollFlingAnimator)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_FlingAfterInitialized_DrawnUi_Draw_ScrollFlingAnimator_">SkiaScroll.FlingAfterInitialized(ScrollFlingAnimator)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_OrderedScrollTo">SkiaScroll.OrderedScrollTo</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_OrderedScrollToIndex">SkiaScroll.OrderedScrollToIndex</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ScrollToOffset_System_Numerics_Vector2_System_Single_">SkiaScroll.ScrollToOffset(Vector2, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_MoveToY_System_Single_">SkiaScroll.MoveToY(float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_MoveToX_System_Single_">SkiaScroll.MoveToX(float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ScrollToIndex_System_Int32_System_Boolean_DrawnUi_Draw_RelativePositionType_">SkiaScroll.ScrollToIndex(int, bool, RelativePositionType)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ExecuteScrollToOrder">SkiaScroll.ExecuteScrollToOrder()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ExecuteScrollToIndexOrder">SkiaScroll.ExecuteScrollToIndexOrder()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_SetContentOffset_System_Numerics_Vector2_System_Boolean_">SkiaScroll.SetContentOffset(Vector2, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ScrollTo_System_Single_System_Single_System_Single_">SkiaScroll.ScrollTo(float, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ScrollToTop_System_Single_">SkiaScroll.ScrollToTop(float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_ScrollToBottom_System_Single_">SkiaScroll.ScrollToBottom(float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_Snapped">SkiaScroll.Snapped</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_IsSnapping">SkiaScroll.IsSnapping</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_IsAnimating">SkiaScroll.IsAnimating</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_IsBouncing">SkiaScroll.IsBouncing</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_IncrementalMeasureAheadCount">SkiaScroll.IncrementalMeasureAheadCount</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_IncrementalMeasureBatchSize">SkiaScroll.IncrementalMeasureBatchSize</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_MeasurementTriggerDistance">SkiaScroll.MeasurementTriggerDistance</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_CheckForIncrementalMeasurementTrigger">SkiaScroll.CheckForIncrementalMeasurementTrigger()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_TriggerIncrementalMeasurement_DrawnUi_Draw_SkiaLayout_">SkiaScroll.TriggerIncrementalMeasurement(SkiaLayout)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetEnumerator">SkiaControl.GetEnumerator()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Add_Microsoft_Maui_IView_">SkiaControl.Add(IView)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Clear">SkiaControl.Clear()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Contains_Microsoft_Maui_IView_">SkiaControl.Contains(IView)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CopyTo_Microsoft_Maui_IView___System_Int32_">SkiaControl.CopyTo(IView[], int)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Remove_Microsoft_Maui_IView_">SkiaControl.Remove(IView)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Count">SkiaControl.Count</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsReadOnly">SkiaControl.IsReadOnly</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IndexOf_Microsoft_Maui_IView_">SkiaControl.IndexOf(IView)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Insert_System_Int32_Microsoft_Maui_IView_">SkiaControl.Insert(int, IView)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RemoveAt_System_Int32_">SkiaControl.RemoveAt(int)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Item_System_Int32_">SkiaControl.this[int]</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetVisualChildren">SkiaControl.GetVisualChildren()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetVisualParent">SkiaControl.GetVisualParent()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ReportHotreloadChildAdded_DrawnUi_Draw_SkiaControl_">SkiaControl.ReportHotreloadChildAdded(SkiaControl)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ReportHotreloadChildRemoved_DrawnUi_Draw_SkiaControl_">SkiaControl.ReportHotreloadChildRemoved(SkiaControl)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_TransparentColor">SkiaControl.TransparentColor</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_WhiteColor">SkiaControl.WhiteColor</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_BlackColor">SkiaControl.BlackColor</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RedColor">SkiaControl.RedColor</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_UsingControlStyle">SkiaControl.UsingControlStyle</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ClearColorProperty">SkiaControl.ClearColorProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ClearColor">SkiaControl.ClearColor</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AddSubView_DrawnUi_Draw_SkiaControl_">SkiaControl.AddSubView(SkiaControl)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RemoveSubView_DrawnUi_Draw_SkiaControl_">SkiaControl.RemoveSubView(SkiaControl)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnLayoutChanged">SkiaControl.OnLayoutChanged()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SetupGradient_SkiaSharp_SKPaint_DrawnUi_Draw_SkiaGradient_SkiaSharp_SKRect_">SkiaControl.SetupGradient(SKPaint, SkiaGradient, SKRect)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CreateShadow_DrawnUi_Draw_SkiaShadow_System_Single_">SkiaControl.CreateShadow(SkiaShadow, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_UpdatePlatformShadow">SkiaControl.UpdatePlatformShadow()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_PlatformShadow">SkiaControl.PlatformShadow</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_HasPlatformClip">SkiaControl.HasPlatformClip()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetDensity">SkiaControl.GetDensity()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_TransferState_Microsoft_Maui_IView_">SkiaControl.TransferState(IView)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Reload">SkiaControl.Reload()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ReloadHandler">SkiaControl.ReloadHandler</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_UseCacheProperty">SkiaControl.UseCacheProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_UseCache">SkiaControl.UseCache</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AllowCachingProperty">SkiaControl.AllowCachingProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AllowCaching">SkiaControl.AllowCaching</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RenderObject">SkiaControl.RenderObject</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnCacheCreated">SkiaControl.OnCacheCreated()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnCacheDestroyed">SkiaControl.OnCacheDestroyed()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CreatedCache">SkiaControl.CreatedCache</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DestroyRenderingObject">SkiaControl.DestroyRenderingObject()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DrawRenderObject_DrawnUi_Draw_DrawingContext_System_Single_System_Single_DrawnUi_Draw_CachedObject_">SkiaControl.DrawRenderObject(DrawingContext, float, float, CachedObject)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsRenderObjectValid_SkiaSharp_SKSize_DrawnUi_Draw_CachedObject_">SkiaControl.IsRenderObjectValid(SKSize, CachedObject)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CreateRenderedObject_DrawnUi_Draw_DrawingContext_SkiaSharp_SKRect_System_Boolean_">SkiaControl.CreateRenderedObject(DrawingContext, SKRect, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RasterizeToCache_DrawnUi_Draw_DrawingContext_">SkiaControl.RasterizeToCache(DrawingContext)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CheckCachedObjectValid_DrawnUi_Draw_CachedObject_SkiaSharp_SKRect_DrawnUi_Draw_SkiaDrawingContext_">SkiaControl.CheckCachedObjectValid(CachedObject, SKRect, SkiaDrawingContext)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_UsingCacheType">SkiaControl.UsingCacheType</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CreateRenderingObject_DrawnUi_Draw_DrawingContext_SkiaSharp_SKRect_DrawnUi_Draw_CachedObject_DrawnUi_Draw_SkiaCacheType_System_Action_DrawnUi_Draw_DrawingContext__">SkiaControl.CreateRenderingObject(DrawingContext, SKRect, CachedObject, SkiaCacheType, Action&lt;DrawingContext&gt;)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DelegateDrawCache">SkiaControl.DelegateDrawCache</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DrawRenderObjectInternal_DrawnUi_Draw_DrawingContext_DrawnUi_Draw_CachedObject_">SkiaControl.DrawRenderObjectInternal(DrawingContext, CachedObject)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsCacheImage">SkiaControl.IsCacheImage</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsCacheOperations">SkiaControl.IsCacheOperations</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_UseRenderingObject_DrawnUi_Draw_DrawingContext_SkiaSharp_SKRect_">SkiaControl.UseRenderingObject(DrawingContext, SKRect)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CacheValidity">SkiaControl.CacheValidity</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetOffscreenRenderingAction">SkiaControl.GetOffscreenRenderingAction()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_PushToOffscreenRendering_System_Action_System_Threading_CancellationToken_">SkiaControl.PushToOffscreenRendering(Action, CancellationToken)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_semaphoreOffsecreenProcess">SkiaControl.semaphoreOffsecreenProcess</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ProcessOffscreenCacheRenderingAsync">SkiaControl.ProcessOffscreenCacheRenderingAsync()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_NeedUpdateFrontCache">SkiaControl.NeedUpdateFrontCache</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_InvalidateCache">SkiaControl.InvalidateCache()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_InvalidateCacheWithPrevious">SkiaControl.InvalidateCacheWithPrevious()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetCacheRecordingArea_SkiaSharp_SKRect_">SkiaControl.GetCacheRecordingArea(SKRect)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetCacheArea_SkiaSharp_SKRect_">SkiaControl.GetCacheArea(SKRect)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DrawUsingRenderObject_DrawnUi_Draw_DrawingContext_System_Single_System_Single_">SkiaControl.DrawUsingRenderObject(DrawingContext, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_PrepareNode_DrawnUi_Draw_DrawingContext_System_Single_System_Single_">SkiaControl.PrepareNode(DrawingContext, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DrawDirectInternal_DrawnUi_Draw_DrawingContext_SkiaSharp_SKRect_">SkiaControl.DrawDirectInternal(DrawingContext, SKRect)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CreateRenderingObjectAndPaint_DrawnUi_Draw_DrawingContext_SkiaSharp_SKRect_System_Action_DrawnUi_Draw_DrawingContext__">SkiaControl.CreateRenderingObjectAndPaint(DrawingContext, SKRect, Action&lt;DrawingContext&gt;)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AttachEffects">SkiaControl.AttachEffects()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnVisualEffectsChanged">SkiaControl.OnVisualEffectsChanged()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_EffectsGestureProcessors">SkiaControl.EffectsGestureProcessors</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_EffectsState">SkiaControl.EffectsState</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_EffectRenderers">SkiaControl.EffectRenderers</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_EffectImageFilter">SkiaControl.EffectImageFilter</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_EffectColorFilter">SkiaControl.EffectColorFilter</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_EffectPostRenderer">SkiaControl.EffectPostRenderer</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_VisualEffectsProperty">SkiaControl.VisualEffectsProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_VisualEffects">SkiaControl.VisualEffects</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DisableEffectsProperty">SkiaControl.DisableEffectsProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DisableEffects">SkiaControl.DisableEffects</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_InvalidateInternal">SkiaControl.InvalidateInternal()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Invalidate">SkiaControl.Invalidate()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ShouldInvalidateByChildren">SkiaControl.ShouldInvalidateByChildren</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_InvalidateParents">SkiaControl.InvalidateParents()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_InvalidatedParent">SkiaControl.InvalidatedParent</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_InvalidateParent">SkiaControl.InvalidateParent()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_InvalidateByChild_DrawnUi_Draw_SkiaControl_">SkiaControl.InvalidateByChild(SkiaControl)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DirtyChildrenTracker">SkiaControl.DirtyChildrenTracker</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DirtyChildrenInternal">SkiaControl.DirtyChildrenInternal</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_VisualLayer">SkiaControl.VisualLayer</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CachedImage">SkiaControl.CachedImage</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Left">SkiaControl.Left</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Top">SkiaControl.Top</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ControlStyleProperty">SkiaControl.ControlStyleProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ControlStyle">SkiaControl.ControlStyle</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ClippedEffectsWithProperty">SkiaControl.ClippedEffectsWithProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ClippedEffectsWith">SkiaControl.ClippedEffectsWith</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ClipWithProperty">SkiaControl.ClipWithProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ClipWith">SkiaControl.ClipWith</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ChildrenProperty">SkiaControl.ChildrenProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Children">SkiaControl.Children</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DefaultBlendMode">SkiaControl.DefaultBlendMode</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsVisibleInViewTree">SkiaControl.IsVisibleInViewTree()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetPositionOnCanvasInPoints">SkiaControl.GetPositionOnCanvasInPoints()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetFuturePositionOnCanvasInPoints_System_Boolean_">SkiaControl.GetFuturePositionOnCanvasInPoints(bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetPositionOnCanvas">SkiaControl.GetPositionOnCanvas()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetFuturePositionOnCanvas">SkiaControl.GetFuturePositionOnCanvas()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetSelfDrawingPosition">SkiaControl.GetSelfDrawingPosition()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_BuildSelfDrawingPosition_SkiaSharp_SKPoint_DrawnUi_Draw_SkiaControl_System_Boolean_">SkiaControl.BuildSelfDrawingPosition(SKPoint, SkiaControl, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_BuildDrawingOffsetRecursive_SkiaSharp_SKPoint_DrawnUi_Draw_SkiaControl_System_Boolean_System_Boolean_">SkiaControl.BuildDrawingOffsetRecursive(SKPoint, SkiaControl, bool, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_BuildDrawnOffsetRecursive_SkiaSharp_SKPoint_DrawnUi_Draw_SkiaControl_System_Boolean_System_Boolean_">SkiaControl.BuildDrawnOffsetRecursive(SKPoint, SkiaControl, bool, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DebugString">SkiaControl.DebugString</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CanDraw">SkiaControl.CanDraw</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SkipRendering">SkiaControl.SkipRendering</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DefaultContentCreated">SkiaControl.DefaultContentCreated</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CreateDefaultContent">SkiaControl.CreateDefaultContent()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SetDefaultMinimumContentSize_System_Double_System_Double_">SkiaControl.SetDefaultMinimumContentSize(double, double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SetDefaultContentSize_System_Double_System_Double_">SkiaControl.SetDefaultContentSize(double, double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GenerateParentChain">SkiaControl.GenerateParentChain()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SetVisualTransform_DrawnUi_Infrastructure_VisualTransform_">SkiaControl.SetVisualTransform(VisualTransform)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CommitInvalidations">SkiaControl.CommitInvalidations()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SuperViewChanged">SkiaControl.SuperViewChanged()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_PostponeInvalidation_System_String_System_Action_">SkiaControl.PostponeInvalidation(string, Action)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetRenderingScaleFor_System_Single_System_Single_">SkiaControl.GetRenderingScaleFor(float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetRenderingScaleFor_System_Single_">SkiaControl.GetRenderingScaleFor(float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AnimateAsync_System_Action_System_Double__System_Action_System_Single_Microsoft_Maui_Easing_System_Threading_CancellationTokenSource_">SkiaControl.AnimateAsync(Action&lt;double&gt;, Action, float, Easing, CancellationTokenSource)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_FadeToAsync_System_Double_System_Single_Microsoft_Maui_Easing_System_Threading_CancellationTokenSource_">SkiaControl.FadeToAsync(double, float, Easing, CancellationTokenSource)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ScaleToAsync_System_Double_System_Double_System_Single_Microsoft_Maui_Easing_System_Threading_CancellationTokenSource_">SkiaControl.ScaleToAsync(double, double, float, Easing, CancellationTokenSource)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_TranslateToAsync_System_Double_System_Double_System_Single_Microsoft_Maui_Easing_System_Threading_CancellationTokenSource_">SkiaControl.TranslateToAsync(double, double, float, Easing, CancellationTokenSource)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RotateToAsync_System_Double_System_UInt32_Microsoft_Maui_Easing_System_Threading_CancellationTokenSource_">SkiaControl.RotateToAsync(double, uint, Easing, CancellationTokenSource)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnPrintDebug">SkiaControl.OnPrintDebug()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_PrintDebug_System_String_">SkiaControl.PrintDebug(string)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DebugRenderingProperty">SkiaControl.DebugRenderingProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DebugRendering">SkiaControl.DebugRendering</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AnimateRangeAsync_System_Action_System_Double__System_Double_System_Double_System_Double_Microsoft_Maui_Easing_System_Threading_CancellationToken_System_Boolean_">SkiaControl.AnimateRangeAsync(Action&lt;double&gt;, double, double, double, Easing, CancellationToken, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_NotValidPoint">SkiaControl.NotValidPoint()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_PointIsValid_SkiaSharp_SKPoint_">SkiaControl.PointIsValid(SKPoint)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SizeRequest">SkiaControl.SizeRequest</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AdaptWidthConstraintToRequest_System_Single_Microsoft_Maui_Thickness_System_Double_">SkiaControl.AdaptWidthConstraintToRequest(float, Thickness, double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AdaptHeightContraintToRequest_System_Single_Microsoft_Maui_Thickness_System_Double_">SkiaControl.AdaptHeightContraintToRequest(float, Thickness, double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetMeasuringConstraints_DrawnUi_Draw_MeasureRequest_">SkiaControl.GetMeasuringConstraints(MeasureRequest)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AdaptConstraintToContentRequest_System_Single_System_Double_System_Double_System_Boolean_System_Double_System_Double_System_Single_System_Boolean_">SkiaControl.AdaptConstraintToContentRequest(float, double, double, bool, double, double, float, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AdaptWidthConstraintToContentRequest_DrawnUi_Infrastructure_MeasuringConstraints_System_Single_System_Boolean_">SkiaControl.AdaptWidthConstraintToContentRequest(MeasuringConstraints, float, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AdaptHeightConstraintToContentRequest_DrawnUi_Infrastructure_MeasuringConstraints_System_Single_System_Boolean_">SkiaControl.AdaptHeightConstraintToContentRequest(MeasuringConstraints, float, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AdaptWidthConstraintToContentRequest_System_Single_DrawnUi_Draw_ScaledSize_System_Double_">SkiaControl.AdaptWidthConstraintToContentRequest(float, ScaledSize, double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AdaptHeightConstraintToContentRequest_System_Single_DrawnUi_Draw_ScaledSize_System_Double_">SkiaControl.AdaptHeightConstraintToContentRequest(float, ScaledSize, double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AdaptToContraints_SkiaSharp_SKRect_System_Double_System_Double_System_Double_System_Double_">SkiaControl.AdaptToContraints(SKRect, double, double, double, double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AdaptSizeRequestToContent_System_Double_System_Double_">SkiaControl.AdaptSizeRequestToContent(double, double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetTopParentView">SkiaControl.GetTopParentView()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetParentElement_DrawnUi_Draw_IDrawnBase_">SkiaControl.GetParentElement(IDrawnBase)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GestureIsInside_AppoMobi_Maui_Gestures_TouchActionEventArgs_System_Single_System_Single_">SkiaControl.GestureIsInside(TouchActionEventArgs, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GestureStartedInside_AppoMobi_Maui_Gestures_TouchActionEventArgs_System_Single_System_Single_">SkiaControl.GestureStartedInside(TouchActionEventArgs, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsPointInside_System_Single_System_Single_System_Single_">SkiaControl.IsPointInside(float, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsPointInside_SkiaSharp_SKRect_System_Single_System_Single_System_Single_">SkiaControl.IsPointInside(SKRect, float, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsPixelInside_SkiaSharp_SKRect_System_Single_System_Single_">SkiaControl.IsPixelInside(SKRect, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsPixelInside_System_Single_System_Single_">SkiaControl.IsPixelInside(float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CheckGestureIsInsideChild_DrawnUi_Draw_SkiaControl_AppoMobi_Maui_Gestures_TouchActionEventArgs_System_Single_System_Single_">SkiaControl.CheckGestureIsInsideChild(SkiaControl, TouchActionEventArgs, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CheckGestureIsForChild_DrawnUi_Draw_SkiaControl_AppoMobi_Maui_Gestures_TouchActionEventArgs_System_Single_System_Single_">SkiaControl.CheckGestureIsForChild(SkiaControl, TouchActionEventArgs, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_LockIterateListeners">SkiaControl.LockIterateListeners</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_LockChildrenGesturesProperty">SkiaControl.LockChildrenGesturesProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_LockChildrenGestures">SkiaControl.LockChildrenGestures</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CheckChildrenGesturesLocked_AppoMobi_Maui_Gestures_TouchActionResult_">SkiaControl.CheckChildrenGesturesLocked(TouchActionResult)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnSkiaGestureEvent_DrawnUi_Draw_SkiaGesturesParameters_DrawnUi_Draw_GestureEventProcessingInfo_">SkiaControl.OnSkiaGestureEvent(SkiaGesturesParameters, GestureEventProcessingInfo)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CommandChildTappedProperty">SkiaControl.CommandChildTappedProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CommandChildTapped">SkiaControl.CommandChildTapped</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Tapped">SkiaControl.Tapped</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_TouchEffectColorProperty">SkiaControl.TouchEffectColorProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_TouchEffectColor">SkiaControl.TouchEffectColor</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AnimationTappedProperty">SkiaControl.AnimationTappedProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AnimationTapped">SkiaControl.AnimationTapped</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_TransformViewProperty">SkiaControl.TransformViewProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_TransformView">SkiaControl.TransformView</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SendTapped_System_Object_DrawnUi_Draw_SkiaGesturesParameters_DrawnUi_Draw_GestureEventProcessingInfo_System_Boolean_">SkiaControl.SendTapped(object, SkiaGesturesParameters, GestureEventProcessingInfo, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ChildTapped">SkiaControl.ChildTapped</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_TransformPointToLocalSpace_SkiaSharp_SKPoint_">SkiaControl.TransformPointToLocalSpace(SKPoint)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsGestureInside_DrawnUi_Draw_GestureEventProcessingInfo_">SkiaControl.IsGestureInside(GestureEventProcessingInfo)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnGestures">SkiaControl.OnGestures</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_BlockGesturesBelowProperty">SkiaControl.BlockGesturesBelowProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_BlockGesturesBelow">SkiaControl.BlockGesturesBelow</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_UpdateLocks">SkiaControl.UpdateLocks</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_UnlockUpdate">SkiaControl.UnlockUpdate()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_LockUpdate_System_Boolean_">SkiaControl.LockUpdate(bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_NativeParent">SkiaControl.NativeParent</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ParentProperty">SkiaControl.ParentProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Parent">SkiaControl.Parent</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AlignContentVerticalProperty">SkiaControl.AlignContentVerticalProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AlignContentVertical">SkiaControl.AlignContentVertical</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AlignContentHorizontalProperty">SkiaControl.AlignContentHorizontalProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AlignContentHorizontal">SkiaControl.AlignContentHorizontal</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_VerticalOptionsProperty">SkiaControl.VerticalOptionsProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_VerticalOptions">SkiaControl.VerticalOptions</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_HorizontalOptionsProperty">SkiaControl.HorizontalOptionsProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_HorizontalOptions">SkiaControl.HorizontalOptions</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnParentVisibilityChanged_System_Boolean_">SkiaControl.OnParentVisibilityChanged(bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_PropagateVisibilityChanged_System_Boolean_">SkiaControl.PropagateVisibilityChanged(bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_VisibilityChanged">SkiaControl.VisibilityChanged</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SendVisibilityChanged">SkiaControl.SendVisibilityChanged()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnVisibilityChanged_System_Boolean_">SkiaControl.OnVisibilityChanged(bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnDisposing">SkiaControl.OnDisposing()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_lockPausingAnimators">SkiaControl.lockPausingAnimators</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_PauseAllAnimators">SkiaControl.PauseAllAnimators()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ResumePausedAnimators">SkiaControl.ResumePausedAnimators()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsGhostProperty">SkiaControl.IsGhostProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsGhost">SkiaControl.IsGhost</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IgnoreChildrenInvalidationsProperty">SkiaControl.IgnoreChildrenInvalidationsProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IgnoreChildrenInvalidations">SkiaControl.IgnoreChildrenInvalidations</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_FillGradientProperty">SkiaControl.FillGradientProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_FillGradient">SkiaControl.FillGradient</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_HasFillGradient">SkiaControl.HasFillGradient</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetSizeRequest_System_Single_System_Single_System_Boolean_">SkiaControl.GetSizeRequest(float, float, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SmartMax_System_Single_System_Single_">SkiaControl.SmartMax(float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SmartMin_System_Single_System_Single_">SkiaControl.SmartMin(float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ViewportHeightLimitProperty">SkiaControl.ViewportHeightLimitProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ViewportHeightLimit">SkiaControl.ViewportHeightLimit</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ViewportWidthLimitProperty">SkiaControl.ViewportWidthLimitProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ViewportWidthLimit">SkiaControl.ViewportWidthLimit</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Height">SkiaControl.Height</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Width">SkiaControl.Width</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Scale">SkiaControl.Scale</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_TagProperty">SkiaControl.TagProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Tag">SkiaControl.Tag</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_LockRatioProperty">SkiaControl.LockRatioProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_LockRatio">SkiaControl.LockRatio</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_HeightRequestRatioProperty">SkiaControl.HeightRequestRatioProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_HeightRequestRatio">SkiaControl.HeightRequestRatio</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_WidthRequestRatioProperty">SkiaControl.WidthRequestRatioProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_WidthRequestRatio">SkiaControl.WidthRequestRatio</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_HorizontalFillRatioProperty">SkiaControl.HorizontalFillRatioProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_HorizontalFillRatio">SkiaControl.HorizontalFillRatio</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_VerticalFillRatioProperty">SkiaControl.VerticalFillRatioProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_VerticalFillRatio">SkiaControl.VerticalFillRatio</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_HorizontalPositionOffsetRatioProperty">SkiaControl.HorizontalPositionOffsetRatioProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_HorizontalPositionOffsetRatio">SkiaControl.HorizontalPositionOffsetRatio</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_VerticalPositionOffsetRatioProperty">SkiaControl.VerticalPositionOffsetRatioProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_VerticalPositionOffsetRatio">SkiaControl.VerticalPositionOffsetRatio</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_FillBlendModeProperty">SkiaControl.FillBlendModeProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_FillBlendMode">SkiaControl.FillBlendMode</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SkewXProperty">SkiaControl.SkewXProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SkewX">SkiaControl.SkewX</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SkewYProperty">SkiaControl.SkewYProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SkewY">SkiaControl.SkewY</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_TranslationZProperty">SkiaControl.TranslationZProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_TranslationZ">SkiaControl.TranslationZ</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RotationZProperty">SkiaControl.RotationZProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RotationZ">SkiaControl.RotationZ</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Perspective1Property">SkiaControl.Perspective1Property</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Perspective1">SkiaControl.Perspective1</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Perspective2Property">SkiaControl.Perspective2Property</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Perspective2">SkiaControl.Perspective2</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ParentChanged">SkiaControl.ParentChanged</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AdjustClippingProperty">SkiaControl.AdjustClippingProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AdjustClipping">SkiaControl.AdjustClipping</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_PaddingProperty">SkiaControl.PaddingProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Padding">SkiaControl.Padding</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_MarginProperty">SkiaControl.MarginProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Margin">SkiaControl.Margin</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AddMarginTopProperty">SkiaControl.AddMarginTopProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AddMarginTop">SkiaControl.AddMarginTop</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AddMarginBottomProperty">SkiaControl.AddMarginBottomProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AddMarginBottom">SkiaControl.AddMarginBottom</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AddMarginLeftProperty">SkiaControl.AddMarginLeftProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AddMarginLeft">SkiaControl.AddMarginLeft</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AddMarginRightProperty">SkiaControl.AddMarginRightProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AddMarginRight">SkiaControl.AddMarginRight</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Margins">SkiaControl.Margins</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SpacingProperty">SkiaControl.SpacingProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Spacing">SkiaControl.Spacing</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AddTranslationYProperty">SkiaControl.AddTranslationYProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AddTranslationY">SkiaControl.AddTranslationY</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AddTranslationXProperty">SkiaControl.AddTranslationXProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AddTranslationX">SkiaControl.AddTranslationX</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ExpandDirtyRegionProperty">SkiaControl.ExpandDirtyRegionProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ExpandDirtyRegion">SkiaControl.ExpandDirtyRegion</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_LockFocusProperty">SkiaControl.LockFocusProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_LockFocus">SkiaControl.LockFocus</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsClippedToBoundsProperty">SkiaControl.IsClippedToBoundsProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsClippedToBounds">SkiaControl.IsClippedToBounds</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ClipEffectsProperty">SkiaControl.ClipEffectsProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ClipEffects">SkiaControl.ClipEffects</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_BindableTriggerProperty">SkiaControl.BindableTriggerProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnTriggerChanged">SkiaControl.OnTriggerChanged()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_BindableTrigger">SkiaControl.BindableTrigger</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Value1Property">SkiaControl.Value1Property</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Value1">SkiaControl.Value1</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Value2Property">SkiaControl.Value2Property</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Value2">SkiaControl.Value2</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Value3Property">SkiaControl.Value3Property</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Value3">SkiaControl.Value3</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Value4Property">SkiaControl.Value4Property</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Value4">SkiaControl.Value4</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RenderingScaleProperty">SkiaControl.RenderingScaleProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RenderingScale">SkiaControl.RenderingScale</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RenderedAtDestination">SkiaControl.RenderedAtDestination</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnScaleChanged">SkiaControl.OnScaleChanged()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SetPropertyValue_Microsoft_Maui_Controls_BindableProperty_System_Object_">SkiaControl.SetPropertyValue(BindableProperty, object)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Uid">SkiaControl.Uid</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DegreesToRadians_System_Single_">SkiaControl.DegreesToRadians(float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RadiansToDegrees_System_Single_">SkiaControl.RadiansToDegrees(float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DegreesToRadians_System_Double_">SkiaControl.DegreesToRadians(double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RadiansToDegrees_System_Double_">SkiaControl.RadiansToDegrees(double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_LinearGradientAngleToPoints_System_Double_">SkiaControl.LinearGradientAngleToPoints(double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DisposalDelay">SkiaControl.DisposalDelay</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsAlive">SkiaControl.IsAlive</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DisposeObject">SkiaControl.DisposeObject()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DisposeObject_System_IDisposable_">SkiaControl.DisposeObject(IDisposable)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnSizeChanged">SkiaControl.OnSizeChanged()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Clipping">SkiaControl.Clipping</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Hero">SkiaControl.Hero</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ContextIndex">SkiaControl.ContextIndex</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsRootView">SkiaControl.IsRootView()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DefineAvailableSize_SkiaSharp_SKRect_System_Single_System_Single_System_Single_System_Boolean_">SkiaControl.DefineAvailableSize(SKRect, float, float, float, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsLayoutDirty">SkiaControl.IsLayoutDirty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SnapToPixel_System_Double_System_Double_">SkiaControl.SnapToPixel(double, double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SnapSizeToPixel_SkiaSharp_SKSize_System_Double_">SkiaControl.SnapSizeToPixel(SKSize, double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SnapPointToPixel_SkiaSharp_SKPoint_System_Double_">SkiaControl.SnapPointToPixel(SKPoint, double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SnapRectCenter_SkiaSharp_SKRect_System_Single_">SkiaControl.SnapRectCenter(SKRect, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SnapRectToPixels_SkiaSharp_SKRect_System_Single_">SkiaControl.SnapRectToPixels(SKRect, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RoundCenterAlignment">SkiaControl.RoundCenterAlignment</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CalculateLayout_SkiaSharp_SKRect_System_Single_System_Single_System_Single_">SkiaControl.CalculateLayout(SKRect, float, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ContentSize">SkiaControl.ContentSize</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_WasMeasured">SkiaControl.WasMeasured</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnDrawingSizeChanged">SkiaControl.OnDrawingSizeChanged()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AdaptCachedLayout_SkiaSharp_SKRect_System_Single_">SkiaControl.AdaptCachedLayout(SKRect, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl__lastMeasuredForWidth">SkiaControl._lastMeasuredForWidth</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl__lastMeasuredForHeight">SkiaControl._lastMeasuredForHeight</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DrawingRect">SkiaControl.DrawingRect</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DirtyRegion">SkiaControl.DirtyRegion</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_HitIsInside_System_Single_System_Single_">SkiaControl.HitIsInside(float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_HitBoxAuto">SkiaControl.HitBoxAuto</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ApplyTransforms_SkiaSharp_SKRect_">SkiaControl.ApplyTransforms(SKRect)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RenderTransformMatrix">SkiaControl.RenderTransformMatrix</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_InverseRenderTransformMatrix">SkiaControl.InverseRenderTransformMatrix</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ApplyTransformationMatrix_DrawnUi_Draw_SkiaDrawingContext_">SkiaControl.ApplyTransformationMatrix(SkiaDrawingContext)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CreateTransformationMatrix_DrawnUi_Draw_SkiaDrawingContext_SkiaSharp_SKRect_">SkiaControl.CreateTransformationMatrix(SkiaDrawingContext, SKRect)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ApplyTransforms_DrawnUi_Draw_SkiaDrawingContext_SkiaSharp_SKRect_">SkiaControl.ApplyTransforms(SkiaDrawingContext, SKRect)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_TranslateInputDirectOffsetToPoints_Microsoft_Maui_Graphics_PointF_SkiaSharp_SKPoint_">SkiaControl.TranslateInputDirectOffsetToPoints(PointF, SKPoint)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_TranslateInputOffsetToPixels_Microsoft_Maui_Graphics_PointF_SkiaSharp_SKPoint_">SkiaControl.TranslateInputOffsetToPixels(PointF, SKPoint)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_TranslateInputCoords_SkiaSharp_SKPoint_System_Boolean_">SkiaControl.TranslateInputCoords(SKPoint, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CalculatePositionOffset_System_Boolean_System_Boolean_System_Boolean_">SkiaControl.CalculatePositionOffset(bool, bool, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CalculateFuturePositionOffset_System_Boolean_System_Boolean_System_Boolean_">SkiaControl.CalculateFuturePositionOffset(bool, bool, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ArrangedDestination">SkiaControl.ArrangedDestination</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_LayoutIsReady">SkiaControl.LayoutIsReady</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Disposing">SkiaControl.Disposing</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnLayoutReady">SkiaControl.OnLayoutReady()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsLayoutReady">SkiaControl.IsLayoutReady</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_LayoutReady">SkiaControl.LayoutReady</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CheckIsGhost">SkiaControl.CheckIsGhost()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Layout_DrawnUi_Draw_DrawingContext_SkiaSharp_SKRect_">SkiaControl.Layout(DrawingContext, SKRect)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Layout_DrawnUi_Draw_DrawingContext_System_Single_System_Single_System_Single_System_Single_">SkiaControl.Layout(DrawingContext, float, float, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Layout_DrawnUi_Draw_DrawingContext_System_Int32_System_Int32_System_Int32_System_Int32_">SkiaControl.Layout(DrawingContext, int, int, int, int)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Arrange_SkiaSharp_SKRect_System_Single_System_Single_System_Single_">SkiaControl.Arrange(SKRect, float, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_PostArrange_SkiaSharp_SKRect_System_Single_System_Single_System_Single_">SkiaControl.PostArrange(SKRect, float, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_MeasureChild_DrawnUi_Draw_SkiaControl_System_Double_System_Double_System_Single_">SkiaControl.MeasureChild(SkiaControl, double, double, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_MeasureContent_System_Collections_Generic_IEnumerable_DrawnUi_Draw_SkiaControl__SkiaSharp_SKRect_System_Single_">SkiaControl.MeasureContent(IEnumerable&lt;SkiaControl&gt;, SKRect, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SetInheritedBindingContext_System_Object_">SkiaControl.SetInheritedBindingContext(object)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ApplyingBindingContext">SkiaControl.ApplyingBindingContext</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_BindingContextWasSet">SkiaControl.BindingContextWasSet</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnBindingContextChanged">SkiaControl.OnBindingContextChanged()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Measure_System_Single_System_Single_System_Single_">SkiaControl.Measure(float, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_InitializeDefaultContent_System_Boolean_">SkiaControl.InitializeDefaultContent(bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SetMeasuredAsEmpty_System_Single_">SkiaControl.SetMeasuredAsEmpty(float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetContentSizeForAutosizeInPixels">SkiaControl.GetContentSizeForAutosizeInPixels()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SetMeasuredAdaptToContentSize_DrawnUi_Infrastructure_MeasuringConstraints_System_Single_">SkiaControl.SetMeasuredAdaptToContentSize(MeasuringConstraints, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetSizeInPoints_SkiaSharp_SKSize_System_Single_">SkiaControl.GetSizeInPoints(SKSize, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CreateMeasureRequest_System_Single_System_Single_System_Single_">SkiaControl.CreateMeasureRequest(float, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetMeasuringRectForChildren_System_Single_System_Single_System_Double_">SkiaControl.GetMeasuringRectForChildren(float, float, double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_MeasureAbsolute_SkiaSharp_SKRect_System_Single_">SkiaControl.MeasureAbsolute(SKRect, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_MeasureAbsoluteBase_SkiaSharp_SKRect_System_Single_">SkiaControl.MeasureAbsoluteBase(SKRect, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ContractPixelsRect_SkiaSharp_SKRect_System_Single_Microsoft_Maui_Thickness_">SkiaControl.ContractPixelsRect(SKRect, float, Thickness)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ExpandPixelsRect_SkiaSharp_SKRect_System_Single_Microsoft_Maui_Thickness_">SkiaControl.ExpandPixelsRect(SKRect, float, Thickness)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetDrawingRectForChildren_SkiaSharp_SKRect_System_Double_">SkiaControl.GetDrawingRectForChildren(SKRect, double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetDrawingRectWithMargins_SkiaSharp_SKRect_System_Double_">SkiaControl.GetDrawingRectWithMargins(SKRect, double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_lockMeasured">SkiaControl.lockMeasured</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsMeasuring">SkiaControl.IsMeasuring</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_LockMeasure">SkiaControl.LockMeasure</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SendOnMeasured">SkiaControl.SendOnMeasured()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Measured">SkiaControl.Measured</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_MeasuredSize">SkiaControl.MeasuredSize</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_NeedAutoSize">SkiaControl.NeedAutoSize</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_NeedAutoHeight">SkiaControl.NeedAutoHeight</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_NeedAutoWidth">SkiaControl.NeedAutoWidth</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsDisposed">SkiaControl.IsDisposed</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsDisposing">SkiaControl.IsDisposing</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_NeedDispose">SkiaControl.NeedDispose</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_FindView__1_System_String_">SkiaControl.FindView&lt;TChild&gt;(string)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_FindView__1">SkiaControl.FindView&lt;TChild&gt;()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_FindViewByTag_System_String_">SkiaControl.FindViewByTag(string)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ExecuteUponDisposal">SkiaControl.ExecuteUponDisposal</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ExecuteAfterCreated">SkiaControl.ExecuteAfterCreated</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ExecuteOnPaint">SkiaControl.ExecuteOnPaint</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ThrowIfDisposed">SkiaControl.ThrowIfDisposed()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Dispose_System_Boolean_">SkiaControl.Dispose(bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Dispose">SkiaControl.Dispose()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_PaintErase">SkiaControl.PaintErase</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetNanoseconds">SkiaControl.GetNanoseconds()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnBeforeMeasure">SkiaControl.OnBeforeMeasure()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OptionalOnBeforeDrawing">SkiaControl.OptionalOnBeforeDrawing()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsOverlay">SkiaControl.IsOverlay</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_PostAnimators">SkiaControl.PostAnimators</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_UpdateWhenReturnedFromBackgroundProperty">SkiaControl.UpdateWhenReturnedFromBackgroundProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_UpdateWhenReturnedFromBackground">SkiaControl.UpdateWhenReturnedFromBackground</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnSuperviewShouldRenderChanged_System_Boolean_">SkiaControl.OnSuperviewShouldRenderChanged(bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ApplyMeasureResult">SkiaControl.ApplyMeasureResult()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_PreArrange_SkiaSharp_SKRect_System_Single_System_Single_System_Single_">SkiaControl.PreArrange(SKRect, float, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsRendering">SkiaControl.IsRendering</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_NodeAttached">SkiaControl.NodeAttached</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_FindRenderedNode_DrawnUi_Draw_SkiaControl_">SkiaControl.FindRenderedNode(SkiaControl)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CreateRenderedNode_SkiaSharp_SKRect_System_Single_">SkiaControl.CreateRenderedNode(SKRect, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Render_DrawnUi_Draw_DrawingContext_">SkiaControl.Render(DrawingContext)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Rendered">SkiaControl.Rendered</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_LockDraw">SkiaControl.LockDraw</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_LockRenderObject">SkiaControl.LockRenderObject</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AddPaintArguments_DrawnUi_Draw_DrawingContext_">SkiaControl.AddPaintArguments(DrawingContext)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnBeforeDrawing_DrawnUi_Draw_DrawingContext_">SkiaControl.OnBeforeDrawing(DrawingContext)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnAfterDrawing_DrawnUi_Draw_DrawingContext_">SkiaControl.OnAfterDrawing(DrawingContext)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_X">SkiaControl.X</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Y">SkiaControl.Y</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_FinalizeDrawingWithRenderObject_DrawnUi_Draw_DrawingContext_">SkiaControl.FinalizeDrawingWithRenderObject(DrawingContext)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetPositionOffsetInPoints">SkiaControl.GetPositionOffsetInPoints()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetPositionOffsetInPixels_System_Boolean_System_Boolean_System_Boolean_">SkiaControl.GetPositionOffsetInPixels(bool, bool, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetFuturePositionOffsetInPixels_System_Boolean_System_Boolean_System_Boolean_">SkiaControl.GetFuturePositionOffsetInPixels(bool, bool, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetOffsetInsideControlInPoints_Microsoft_Maui_Graphics_PointF_SkiaSharp_SKPoint_">SkiaControl.GetOffsetInsideControlInPoints(PointF, SKPoint)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetOffsetInsideControlInPixels_Microsoft_Maui_Graphics_PointF_SkiaSharp_SKPoint_">SkiaControl.GetOffsetInsideControlInPixels(PointF, SKPoint)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_LastDrawnAt">SkiaControl.LastDrawnAt</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ExecutePostAnimators_DrawnUi_Draw_DrawingContext_">SkiaControl.ExecutePostAnimators(DrawingContext)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Repaint">SkiaControl.Repaint()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl__paintWithEffects">SkiaControl._paintWithEffects</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl__paintWithOpacity">SkiaControl._paintWithOpacity</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CustomizeLayerPaint">SkiaControl.CustomizeLayerPaint</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Helper3d">SkiaControl.Helper3d</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DrawWithClipAndTransforms_DrawnUi_Draw_DrawingContext_SkiaSharp_SKRect_System_Boolean_System_Boolean_System_Action_DrawnUi_Draw_DrawingContext__">SkiaControl.DrawWithClipAndTransforms(DrawingContext, SKRect, bool, bool, Action&lt;DrawingContext&gt;)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsSimpleRectangle_SkiaSharp_SKPath_">SkiaControl.IsSimpleRectangle(SKPath)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ClipSmart_SkiaSharp_SKCanvas_SkiaSharp_SKPath_SkiaSharp_SKClipOperation_">SkiaControl.ClipSmart(SKCanvas, SKPath, SKClipOperation)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ShouldClipAntialiased">SkiaControl.ShouldClipAntialiased</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_NeedMeasure">SkiaControl.NeedMeasure</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SafePostAction_System_Action_">SkiaControl.SafePostAction(Action)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SafeAction_System_Action_">SkiaControl.SafeAction(Action)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_NeedRemeasuring">SkiaControl.NeedRemeasuring</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_PaintWithShadows_DrawnUi_Draw_DrawingContext_System_Action_">SkiaControl.PaintWithShadows(DrawingContext, Action)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_PaintWithEffects_DrawnUi_Draw_DrawingContext_">SkiaControl.PaintWithEffects(DrawingContext)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnFirstDrawn">SkiaControl.OnFirstDrawn()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_WasFirstTimeDrawn">SkiaControl.WasFirstTimeDrawn</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CreateClip_System_Object_System_Boolean_SkiaSharp_SKPath_">SkiaControl.CreateClip(object, bool, SKPath)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DebugRenderingColor">SkiaControl.DebugRenderingColor</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_UseTranslationY">SkiaControl.UseTranslationY</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_UseTranslationX">SkiaControl.UseTranslationX</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DrawRenderObject_DrawnUi_Draw_DrawingContext_DrawnUi_Draw_CachedObject_">SkiaControl.DrawRenderObject(DrawingContext, CachedObject)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RenderViewsList_DrawnUi_Draw_DrawingContext_System_Collections_Generic_IEnumerable_DrawnUi_Draw_SkiaControl__">SkiaControl.RenderViewsList(DrawingContext, IEnumerable&lt;SkiaControl&gt;)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl__measuredStamp">SkiaControl._measuredStamp</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl__builtRenderTreeStamp">SkiaControl._builtRenderTreeStamp</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RenderTree">SkiaControl.RenderTree</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SetRenderingTree_System_Collections_Generic_List_DrawnUi_Draw_SkiaControlWithRect__">SkiaControl.SetRenderingTree(List&lt;SkiaControlWithRect&gt;)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Invalidated">SkiaControl.Invalidated</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_NeedUpdate">SkiaControl.NeedUpdate</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Superview">SkiaControl.Superview</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DelegateGetOnScreenVisibleArea">SkiaControl.DelegateGetOnScreenVisibleArea</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsParentIndependent">SkiaControl.IsParentIndependent</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_WillNotUpdateParent">SkiaControl.WillNotUpdateParent</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_UpdateInternal">SkiaControl.UpdateInternal()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Update">SkiaControl.Update()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Updated">SkiaControl.Updated</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_StreamFromString_System_String_">SkiaControl.StreamFromString(string)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DeviceUnitsToPixels_System_Double_">SkiaControl.DeviceUnitsToPixels(double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_PixelsToDeviceUnits_System_Double_">SkiaControl.PixelsToDeviceUnits(double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_PaintSystem">SkiaControl.PaintSystem</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SetupBackgroundPaint_SkiaSharp_SKPaint_SkiaSharp_SKRect_">SkiaControl.SetupBackgroundPaint(SKPaint, SKRect)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_PaintTintBackground_SkiaSharp_SKCanvas_SkiaSharp_SKRect_">SkiaControl.PaintTintBackground(SKCanvas, SKRect)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CombineClipping_SkiaSharp_SKPath_SkiaSharp_SKPath_">SkiaControl.CombineClipping(SKPath, SKPath)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ActionWithClipping_SkiaSharp_SKRect_SkiaSharp_SKCanvas_System_Action_">SkiaControl.ActionWithClipping(SKRect, SKCanvas, Action)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CalculateMargins">SkiaControl.CalculateMargins()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetAllMarginsInPixels_System_Single_">SkiaControl.GetAllMarginsInPixels(float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetMarginsInPixels_System_Single_">SkiaControl.GetMarginsInPixels(float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_InvalidateMeasureInternal">SkiaControl.InvalidateMeasureInternal()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CalculateSizeRequest">SkiaControl.CalculateSizeRequest()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_InvalidateChildrenTree_DrawnUi_Draw_SkiaControl_">SkiaControl.InvalidateChildrenTree(SkiaControl)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_InvalidateChildrenTree">SkiaControl.InvalidateChildrenTree()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OutputDebug">SkiaControl.OutputDebug</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_InvalidateWithChildren">SkiaControl.InvalidateWithChildren()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_WillInvalidateMeasure">SkiaControl.WillInvalidateMeasure</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_NeedInvalidateMeasure_Microsoft_Maui_Controls_BindableObject_System_Object_System_Object_">SkiaControl.NeedInvalidateMeasure(BindableObject, object, object)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_NeedDraw_Microsoft_Maui_Controls_BindableObject_System_Object_System_Object_">SkiaControl.NeedDraw(BindableObject, object, object)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_NeedRepaint_Microsoft_Maui_Controls_BindableObject_System_Object_System_Object_">SkiaControl.NeedRepaint(BindableObject, object, object)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_InvalidateMeasure">SkiaControl.InvalidateMeasure()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_NeedInvalidateViewport_Microsoft_Maui_Controls_BindableObject_System_Object_System_Object_">SkiaControl.NeedInvalidateViewport(BindableObject, object, object)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnItemTemplateChanged">SkiaControl.OnItemTemplateChanged()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CreateContentFromTemplate">SkiaControl.CreateContentFromTemplate()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ItemTemplateChanged_Microsoft_Maui_Controls_BindableObject_System_Object_System_Object_">SkiaControl.ItemTemplateChanged(BindableObject, object, object)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetAnimatorsManager">SkiaControl.GetAnimatorsManager()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RegisterAnimator_DrawnUi_Draw_ISkiaAnimator_">SkiaControl.RegisterAnimator(ISkiaAnimator)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_UnregisterAnimator_System_Guid_">SkiaControl.UnregisterAnimator(Guid)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_UnregisterAllAnimatorsByType_System_Type_">SkiaControl.UnregisterAllAnimatorsByType(Type)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_PlayRippleAnimation_Microsoft_Maui_Graphics_Color_System_Double_System_Double_System_Boolean_">SkiaControl.PlayRippleAnimation(Color, double, double, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_PlayShimmerAnimation_Microsoft_Maui_Graphics_Color_System_Single_System_Single_System_Int32_System_Boolean_">SkiaControl.PlayShimmerAnimation(Color, float, float, int, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CreateGradient_SkiaSharp_SKRect_DrawnUi_Draw_SkiaGradient_">SkiaControl.CreateGradient(SKRect, SkiaGradient)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_LastGradient">SkiaControl.LastGradient</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_LastShadow">SkiaControl.LastShadow</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SetupShadow_SkiaSharp_SKPaint_DrawnUi_Draw_SkiaShadow_System_Single_">SkiaControl.SetupShadow(SKPaint, SkiaShadow, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetOrderedSubviews_System_Boolean_">SkiaControl.GetOrderedSubviews(bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetUnorderedSubviews_System_Boolean_">SkiaControl.GetUnorderedSubviews(bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Views">SkiaControl.Views</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_DisposeChildren">SkiaControl.DisposeChildren()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ClearChildren">SkiaControl.ClearChildren()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_InvalidateViewsList">SkiaControl.InvalidateViewsList()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnChildRemoved_Microsoft_Maui_Controls_Element_System_Int32_">SkiaControl.OnChildRemoved(Element, int)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnChildAdded_Microsoft_Maui_Controls_Element_">SkiaControl.OnChildAdded(Element)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnChildAdded_DrawnUi_Draw_SkiaControl_">SkiaControl.OnChildAdded(SkiaControl)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnChildRemoved_DrawnUi_Draw_SkiaControl_">SkiaControl.OnChildRemoved(SkiaControl)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnChildrenChanged">SkiaControl.OnChildrenChanged()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnViewAttached">SkiaControl.OnViewAttached()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnViewDetached">SkiaControl.OnViewDetached()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GestureListenerRegistrationTime">SkiaControl.GestureListenerRegistrationTime</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RegisterGestureListener_DrawnUi_Draw_ISkiaGestureListener_">SkiaControl.RegisterGestureListener(ISkiaGestureListener)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_UnregisterGestureListener_DrawnUi_Draw_ISkiaGestureListener_">SkiaControl.UnregisterGestureListener(ISkiaGestureListener)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GestureListeners">SkiaControl.GestureListeners</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_OnParentChanged_DrawnUi_Draw_IDrawnBase_DrawnUi_Draw_IDrawnBase_">SkiaControl.OnParentChanged(IDrawnBase, IDrawnBase)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ClearParent">SkiaControl.ClearParent()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_StopAnimations">SkiaControl.StopAnimations()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_SetParent_DrawnUi_Draw_IDrawnBase_">SkiaControl.SetParent(IDrawnBase)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RegisterGestureListenersTree_DrawnUi_Draw_SkiaControl_">SkiaControl.RegisterGestureListenersTree(SkiaControl)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_UnregisterGestureListenersTree_DrawnUi_Draw_SkiaControl_">SkiaControl.UnregisterGestureListenersTree(SkiaControl)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ItemTemplateProperty">SkiaControl.ItemTemplateProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ItemTemplate">SkiaControl.ItemTemplate</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ItemTemplateTypeProperty">SkiaControl.ItemTemplateTypeProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_ItemTemplateType">SkiaControl.ItemTemplateType</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AddOrRemoveView_DrawnUi_Draw_SkiaControl_System_Boolean_">SkiaControl.AddOrRemoveView(SkiaControl, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_HasItemTemplate">SkiaControl.HasItemTemplate</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GesturesEffect">SkiaControl.GesturesEffect</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_RescaleAspect_System_Single_System_Single_SkiaSharp_SKRect_DrawnUi_Draw_TransformAspect_">SkiaControl.RescaleAspect(float, float, SKRect, TransformAspect)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_Random">SkiaControl.Random</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_LastArrangedInside">SkiaControl.LastArrangedInside</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl__arrangedViewportHeightLimit">SkiaControl._arrangedViewportHeightLimit</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl__arrangedViewportWidthLimit">SkiaControl._arrangedViewportWidthLimit</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl__lastMeasuredForScale">SkiaControl._lastMeasuredForScale</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetRandomColor">SkiaControl.GetRandomColor()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CompareFloats_System_Single_System_Single_System_Single_">SkiaControl.CompareFloats(float, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CompareDoubles_System_Double_System_Double_System_Double_">SkiaControl.CompareDoubles(double, double, double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CompareRects_SkiaSharp_SKRect_SkiaSharp_SKRect_System_Single_">SkiaControl.CompareRects(SKRect, SKRect, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CompareRectsSize_SkiaSharp_SKRect_SkiaSharp_SKRect_System_Single_">SkiaControl.CompareRectsSize(SKRect, SKRect, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CompareSize_SkiaSharp_SKSize_SkiaSharp_SKSize_System_Single_">SkiaControl.CompareSize(SKSize, SKSize, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_CompareVectors_System_Numerics_Vector2_System_Numerics_Vector2_System_Single_">SkiaControl.CompareVectors(Vector2, Vector2, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AreEqual_System_Double_System_Double_System_Double_">SkiaControl.AreEqual(double, double, double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AreEqual_System_Single_System_Single_System_Single_">SkiaControl.AreEqual(float, float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AreVectorsEqual_System_Numerics_Vector2_System_Numerics_Vector2_System_Single_">SkiaControl.AreVectorsEqual(Vector2, Vector2, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetDirectionType_System_Numerics_Vector2_DrawnUi_Draw_DirectionType_System_Single_">SkiaControl.GetDirectionType(Vector2, DirectionType, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_GetDirectionType_System_Numerics_Vector2_System_Numerics_Vector2_System_Single_">SkiaControl.GetDirectionType(Vector2, Vector2, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AreClose_System_Single_System_Single_">SkiaControl.AreClose(float, float)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_AreClose_System_Double_System_Double_">SkiaControl.AreClose(double, double)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaControl.html#DrawnUi_Draw_SkiaControl_IsOne_System_Double_">SkiaControl.IsOne(double)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.navigationproperty">VisualElement.NavigationProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.styleproperty">VisualElement.StyleProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.inputtransparentproperty">VisualElement.InputTransparentProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isenabledproperty">VisualElement.IsEnabledProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.xproperty">VisualElement.XProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.yproperty">VisualElement.YProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.anchorxproperty">VisualElement.AnchorXProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.anchoryproperty">VisualElement.AnchorYProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.translationxproperty">VisualElement.TranslationXProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.translationyproperty">VisualElement.TranslationYProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.widthproperty">VisualElement.WidthProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.heightproperty">VisualElement.HeightProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.rotationproperty">VisualElement.RotationProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.rotationxproperty">VisualElement.RotationXProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.rotationyproperty">VisualElement.RotationYProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.scaleproperty">VisualElement.ScaleProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.scalexproperty">VisualElement.ScaleXProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.scaleyproperty">VisualElement.ScaleYProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.clipproperty">VisualElement.ClipProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.visualproperty">VisualElement.VisualProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isvisibleproperty">VisualElement.IsVisibleProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.opacityproperty">VisualElement.OpacityProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.backgroundcolorproperty">VisualElement.BackgroundColorProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.backgroundproperty">VisualElement.BackgroundProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.behaviorsproperty">VisualElement.BehaviorsProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.triggersproperty">VisualElement.TriggersProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.widthrequestproperty">VisualElement.WidthRequestProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.heightrequestproperty">VisualElement.HeightRequestProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.minimumwidthrequestproperty">VisualElement.MinimumWidthRequestProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.minimumheightrequestproperty">VisualElement.MinimumHeightRequestProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.maximumwidthrequestproperty">VisualElement.MaximumWidthRequestProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.maximumheightrequestproperty">VisualElement.MaximumHeightRequestProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isfocusedproperty">VisualElement.IsFocusedProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.flowdirectionproperty">VisualElement.FlowDirectionProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.windowproperty">VisualElement.WindowProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.shadowproperty">VisualElement.ShadowProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.zindexproperty">VisualElement.ZIndexProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.batchbegin">VisualElement.BatchBegin()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.batchcommit">VisualElement.BatchCommit()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.focus">VisualElement.Focus()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.measure#microsoft-maui-controls-visualelement-measure(system-double-system-double)">VisualElement.Measure(double, double)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.measure#microsoft-maui-controls-visualelement-measure(system-double-system-double-microsoft-maui-controls-measureflags)">VisualElement.Measure(double, double, MeasureFlags)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.unfocus">VisualElement.Unfocus()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.onchildrenreordered">VisualElement.OnChildrenReordered()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.onmeasure">VisualElement.OnMeasure(double, double)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.onsizeallocated">VisualElement.OnSizeAllocated(double, double)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.sizeallocated">VisualElement.SizeAllocated(double, double)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.changevisualstate">VisualElement.ChangeVisualState()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.refreshisenabledproperty">VisualElement.RefreshIsEnabledProperty()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.arrange">VisualElement.Arrange(Rect)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.arrangeoverride">VisualElement.ArrangeOverride(Rect)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.layout">VisualElement.Layout(Rect)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.invalidatemeasureoverride">VisualElement.InvalidateMeasureOverride()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.measureoverride">VisualElement.MeasureOverride(double, double)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.mapbackgroundcolor">VisualElement.MapBackgroundColor(IViewHandler, IView)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.mapbackgroundimagesource">VisualElement.MapBackgroundImageSource(IViewHandler, IView)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.visual">VisualElement.Visual</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.flowdirection">VisualElement.FlowDirection</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.window">VisualElement.Window</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.anchorx">VisualElement.AnchorX</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.anchory">VisualElement.AnchorY</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.backgroundcolor">VisualElement.BackgroundColor</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.background">VisualElement.Background</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.behaviors">VisualElement.Behaviors</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.bounds">VisualElement.Bounds</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.heightrequest">VisualElement.HeightRequest</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.inputtransparent">VisualElement.InputTransparent</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isenabled">VisualElement.IsEnabled</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isenabledcore">VisualElement.IsEnabledCore</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isfocused">VisualElement.IsFocused</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isvisible">VisualElement.IsVisible</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.minimumheightrequest">VisualElement.MinimumHeightRequest</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.minimumwidthrequest">VisualElement.MinimumWidthRequest</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.maximumheightrequest">VisualElement.MaximumHeightRequest</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.maximumwidthrequest">VisualElement.MaximumWidthRequest</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.opacity">VisualElement.Opacity</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.rotation">VisualElement.Rotation</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.rotationx">VisualElement.RotationX</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.rotationy">VisualElement.RotationY</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.scalex">VisualElement.ScaleX</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.scaley">VisualElement.ScaleY</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.translationx">VisualElement.TranslationX</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.translationy">VisualElement.TranslationY</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.triggers">VisualElement.Triggers</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.widthrequest">VisualElement.WidthRequest</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.clip">VisualElement.Clip</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.resources">VisualElement.Resources</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.frame">VisualElement.Frame</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.handler">VisualElement.Handler</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.shadow">VisualElement.Shadow</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.zindex">VisualElement.ZIndex</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.desiredsize">VisualElement.DesiredSize</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isloaded">VisualElement.IsLoaded</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.childrenreordered">VisualElement.ChildrenReordered</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.focused">VisualElement.Focused</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.measureinvalidated">VisualElement.MeasureInvalidated</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.sizechanged">VisualElement.SizeChanged</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.unfocused">VisualElement.Unfocused</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.loaded">VisualElement.Loaded</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.unloaded">VisualElement.Unloaded</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.navigableelement.onparentset">NavigableElement.OnParentSet()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.navigableelement.navigation">NavigableElement.Navigation</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.styleableelement.style">StyleableElement.Style</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.styleableelement.styleclass">StyleableElement.StyleClass</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.styleableelement.class">StyleableElement.class</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.automationidproperty">Element.AutomationIdProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.classidproperty">Element.ClassIdProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.insertlogicalchild">Element.InsertLogicalChild(int, Element)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.addlogicalchild">Element.AddLogicalChild(Element)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.removelogicalchild">Element.RemoveLogicalChild(Element)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.clearlogicalchildren">Element.ClearLogicalChildren()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.findbyname">Element.FindByName(string)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.removedynamicresource">Element.RemoveDynamicResource(BindableProperty)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.setdynamicresource">Element.SetDynamicResource(BindableProperty, string)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onparentchanging">Element.OnParentChanging(ParentChangingEventArgs)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onparentchanged">Element.OnParentChanged()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onhandlerchanging">Element.OnHandlerChanging(HandlerChangingEventArgs)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onhandlerchanged">Element.OnHandlerChanged()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.mapautomationpropertiesisinaccessibletree">Element.MapAutomationPropertiesIsInAccessibleTree(IElementHandler, Element)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.mapautomationpropertiesexcludedwithchildren">Element.MapAutomationPropertiesExcludedWithChildren(IElementHandler, Element)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.automationid">Element.AutomationId</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.classid">Element.ClassId</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.effects">Element.Effects</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.id">Element.Id</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.styleid">Element.StyleId</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.childadded">Element.ChildAdded</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.childremoved">Element.ChildRemoved</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.descendantadded">Element.DescendantAdded</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.descendantremoved">Element.DescendantRemoved</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.parentchanging">Element.ParentChanging</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.handlerchanging">Element.HandlerChanging</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.handlerchanged">Element.HandlerChanged</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.bindingcontextproperty">BindableObject.BindingContextProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindableproperty)">BindableObject.ClearValue(BindableProperty)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindablepropertykey)">BindableObject.ClearValue(BindablePropertyKey)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.getvalue">BindableObject.GetValue(BindableProperty)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.isset">BindableObject.IsSet(BindableProperty)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.removebinding">BindableObject.RemoveBinding(BindableProperty)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setbinding">BindableObject.SetBinding(BindableProperty, BindingBase)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.applybindings">BindableObject.ApplyBindings()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.onpropertychanging">BindableObject.OnPropertyChanging(string)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.unapplybindings">BindableObject.UnapplyBindings()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindableproperty-system-object)">BindableObject.SetValue(BindableProperty, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindablepropertykey-system-object)">BindableObject.SetValue(BindablePropertyKey, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindableproperty)">BindableObject.CoerceValue(BindableProperty)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindablepropertykey)">BindableObject.CoerceValue(BindablePropertyKey)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.dispatcher">BindableObject.Dispatcher</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.bindingcontext">BindableObject.BindingContext</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.propertychanged">BindableObject.PropertyChanged</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.propertychanging">BindableObject.PropertyChanging</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.bindingcontextchanged">BindableObject.BindingContextChanged</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)">object.Equals(object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)">object.Equals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gethashcode">object.GetHashCode()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gettype">object.GetType()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone">object.MemberwiseClone()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.referenceequals">object.ReferenceEquals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.tostring">object.ToString()</a>
    </div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="DrawnUi.html">DrawnUi</a>.<a class="xref" href="DrawnUi.Draw.html">Draw</a></h6>
  <h6><strong>Assembly</strong>: DrawnUi.Maui.dll</h6>
  <h5 id="DrawnUi_Draw_PlanesScroll_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public class PlanesScroll : VirtualScroll, INotifyPropertyChanged, IEffectControlProvider, IToolTipElement, IContextFlyoutElement, IAnimatable, IVisualElementController, IElementController, IHotReloadableView, IReplaceableView, IView, IElement, ITransform, IReloadHandler, IVisualTreeElement, IContainer, IList&lt;IView&gt;, ICollection&lt;IView&gt;, IEnumerable&lt;IView&gt;, IEnumerable, IHasAfterEffects, ISkiaControl, IDrawnBase, IDisposable, ICanBeUpdatedWithContext, ICanBeUpdated, ISkiaGestureListener, IDefinesViewport, IWithContent</code></pre>
  </div>
  <h3 id="constructors">Constructors
</h3>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_PlanesScroll__ctor.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.PlanesScroll.%23ctor%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Scroll/PlanesScroll.cs/#L28">View Source</a>
  </span>
  <a id="DrawnUi_Draw_PlanesScroll__ctor_" data-uid="DrawnUi.Draw.PlanesScroll.#ctor*"></a>
  <h4 id="DrawnUi_Draw_PlanesScroll__ctor" data-uid="DrawnUi.Draw.PlanesScroll.#ctor">PlanesScroll()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public PlanesScroll()</code></pre>
  </div>
  <h3 id="fields">Fields
</h3>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_PlanesScroll__planeLayoutData.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.PlanesScroll._planeLayoutData%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Scroll/PlanesScroll.cs/#L12">View Source</a>
  </span>
  <h4 id="DrawnUi_Draw_PlanesScroll__planeLayoutData" data-uid="DrawnUi.Draw.PlanesScroll._planeLayoutData">_planeLayoutData</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected Dictionary&lt;Plane, List&lt;PlanesScroll.ViewLayoutInfo&gt;&gt; _planeLayoutData</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2">Dictionary</a>&lt;<a class="xref" href="DrawnUi.Draw.Plane.html">Plane</a>, <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1">List</a>&lt;<a class="xref" href="DrawnUi.Draw.PlanesScroll.html">PlanesScroll</a>.<a class="xref" href="DrawnUi.Draw.PlanesScroll.ViewLayoutInfo.html">ViewLayoutInfo</a>&gt;&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="properties">Properties
</h3>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_PlanesScroll_UseVirtual.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.PlanesScroll.UseVirtual%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Scroll/PlanesScroll.cs/#L33">View Source</a>
  </span>
  <a id="DrawnUi_Draw_PlanesScroll_UseVirtual_" data-uid="DrawnUi.Draw.PlanesScroll.UseVirtual*"></a>
  <h4 id="DrawnUi_Draw_PlanesScroll_UseVirtual" data-uid="DrawnUi.Draw.PlanesScroll.UseVirtual">UseVirtual</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override bool UseVirtual { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="overrides">Overrides</h5>
  <div><a class="xref" href="DrawnUi.Draw.VirtualScroll.html#DrawnUi_Draw_VirtualScroll_UseVirtual">VirtualScroll.UseVirtual</a></div>
  <h3 id="methods">Methods
</h3>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_PlanesScroll_OnMeasured.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.PlanesScroll.OnMeasured%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Scroll/PlanesScroll.cs/#L42">View Source</a>
  </span>
  <a id="DrawnUi_Draw_PlanesScroll_OnMeasured_" data-uid="DrawnUi.Draw.PlanesScroll.OnMeasured*"></a>
  <h4 id="DrawnUi_Draw_PlanesScroll_OnMeasured" data-uid="DrawnUi.Draw.PlanesScroll.OnMeasured">OnMeasured()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected override void OnMeasured()</code></pre>
  </div>
  <h5 class="overrides">Overrides</h5>
  <div><a class="xref" href="DrawnUi.Draw.VirtualScroll.html#DrawnUi_Draw_VirtualScroll_OnMeasured">VirtualScroll.OnMeasured()</a></div>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_PlanesScroll_PaintOnPlane_DrawnUi_Draw_DrawingContext_DrawnUi_Draw_Plane_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.PlanesScroll.PaintOnPlane(DrawnUi.Draw.DrawingContext%2CDrawnUi.Draw.Plane)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Scroll/PlanesScroll.cs/#L140">View Source</a>
  </span>
  <a id="DrawnUi_Draw_PlanesScroll_PaintOnPlane_" data-uid="DrawnUi.Draw.PlanesScroll.PaintOnPlane*"></a>
  <h4 id="DrawnUi_Draw_PlanesScroll_PaintOnPlane_DrawnUi_Draw_DrawingContext_DrawnUi_Draw_Plane_" data-uid="DrawnUi.Draw.PlanesScroll.PaintOnPlane(DrawnUi.Draw.DrawingContext,DrawnUi.Draw.Plane)">PaintOnPlane(DrawingContext, Plane)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected override void PaintOnPlane(DrawingContext context, Plane plane)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="DrawnUi.Draw.DrawingContext.html">DrawingContext</a></td>
        <td><span class="parametername">context</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="DrawnUi.Draw.Plane.html">Plane</a></td>
        <td><span class="parametername">plane</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="overrides">Overrides</h5>
  <div><a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_PaintOnPlane_DrawnUi_Draw_DrawingContext_DrawnUi_Draw_Plane_">SkiaScroll.PaintOnPlane(DrawingContext, Plane)</a></div>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_PlanesScroll_PreparePlane_DrawnUi_Draw_DrawingContext_DrawnUi_Draw_Plane_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.PlanesScroll.PreparePlane(DrawnUi.Draw.DrawingContext%2CDrawnUi.Draw.Plane)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Scroll/PlanesScroll.cs/#L252">View Source</a>
  </span>
  <a id="DrawnUi_Draw_PlanesScroll_PreparePlane_" data-uid="DrawnUi.Draw.PlanesScroll.PreparePlane*"></a>
  <h4 id="DrawnUi_Draw_PlanesScroll_PreparePlane_DrawnUi_Draw_DrawingContext_DrawnUi_Draw_Plane_" data-uid="DrawnUi.Draw.PlanesScroll.PreparePlane(DrawnUi.Draw.DrawingContext,DrawnUi.Draw.Plane)">PreparePlane(DrawingContext, Plane)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected override void PreparePlane(DrawingContext context, Plane plane)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="DrawnUi.Draw.DrawingContext.html">DrawingContext</a></td>
        <td><span class="parametername">context</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="DrawnUi.Draw.Plane.html">Plane</a></td>
        <td><span class="parametername">plane</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="overrides">Overrides</h5>
  <div><a class="xref" href="DrawnUi.Draw.SkiaScroll.html#DrawnUi_Draw_SkiaScroll_PreparePlane_DrawnUi_Draw_DrawingContext_DrawnUi_Draw_Plane_">SkiaScroll.PreparePlane(DrawingContext, Plane)</a></div>
  <h3 id="implements">Implements</h3>
  <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.componentmodel.inotifypropertychanged">INotifyPropertyChanged</a>
  </div>
  <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ieffectcontrolprovider">IEffectControlProvider</a>
  </div>
  <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.itooltipelement">IToolTipElement</a>
  </div>
  <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.icontextflyoutelement">IContextFlyoutElement</a>
  </div>
  <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ianimatable">IAnimatable</a>
  </div>
  <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ivisualelementcontroller">IVisualElementController</a>
  </div>
  <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ielementcontroller">IElementController</a>
  </div>
  <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.hotreload.ihotreloadableview">IHotReloadableView</a>
  </div>
  <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.ireplaceableview">IReplaceableView</a>
  </div>
  <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.iview">IView</a>
  </div>
  <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.ielement">IElement</a>
  </div>
  <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.itransform">ITransform</a>
  </div>
  <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.hotreload.ireloadhandler">IReloadHandler</a>
  </div>
  <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.ivisualtreeelement">IVisualTreeElement</a>
  </div>
  <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.icontainer">IContainer</a>
  </div>
  <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.ilist-1">IList&lt;T&gt;</a>
  </div>
  <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.icollection-1">ICollection&lt;T&gt;</a>
  </div>
  <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1">IEnumerable&lt;T&gt;</a>
  </div>
  <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.ienumerable">IEnumerable</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.IHasAfterEffects.html">IHasAfterEffects</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.ISkiaControl.html">ISkiaControl</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.IDrawnBase.html">IDrawnBase</a>
  </div>
  <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.idisposable">IDisposable</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.ICanBeUpdatedWithContext.html">ICanBeUpdatedWithContext</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.ICanBeUpdated.html">ICanBeUpdated</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.ISkiaGestureListener.html">ISkiaGestureListener</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.IDefinesViewport.html">IDefinesViewport</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.IWithContent.html">IWithContent</a>
  </div>
  <h3 id="extensionmethods">Extension Methods</h3>
  <div>
      <a class="xref" href="DrawnUi.Draw.DrawnExtensions.html#DrawnUi_Draw_DrawnExtensions_GetVelocityRatioForChild_DrawnUi_Draw_IDrawnBase_DrawnUi_Draw_ISkiaControl_">DrawnExtensions.GetVelocityRatioForChild(IDrawnBase, ISkiaControl)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_Adapt__1___0_System_Action___0__">FluentExtensions.Adapt&lt;T&gt;(T, Action&lt;T&gt;)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_AssignNative__1___0___0__">FluentExtensions.AssignNative&lt;T&gt;(T, out T)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_AssignParent__1___0_DrawnUi_Draw_SkiaControl_">FluentExtensions.AssignParent&lt;T&gt;(T, SkiaControl)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_Assign__1___0___0__">FluentExtensions.Assign&lt;T&gt;(T, out T)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_BindProperty__1___0_Microsoft_Maui_Controls_BindableProperty_System_ComponentModel_INotifyPropertyChanged_System_String_Microsoft_Maui_Controls_BindingMode_">FluentExtensions.BindProperty&lt;T&gt;(T, BindableProperty, INotifyPropertyChanged, string, BindingMode)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_BindProperty__1___0_Microsoft_Maui_Controls_BindableProperty_System_String_Microsoft_Maui_Controls_BindingMode_">FluentExtensions.BindProperty&lt;T&gt;(T, BindableProperty, string, BindingMode)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_BindProperty__2___0_Microsoft_Maui_Controls_BindableProperty_System_String_Microsoft_Maui_Controls_IValueConverter_System_Object_Microsoft_Maui_Controls_BindingMode_">FluentExtensions.BindProperty&lt;T, TProperty&gt;(T, BindableProperty, string, IValueConverter, object, BindingMode)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_CenterX__1___0_">FluentExtensions.CenterX&lt;T&gt;(T)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_CenterY__1___0_">FluentExtensions.CenterY&lt;T&gt;(T)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_Center__1___0_">FluentExtensions.Center&lt;T&gt;(T)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_EndX__1___0_">FluentExtensions.EndX&lt;T&gt;(T)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_EndY__1___0_">FluentExtensions.EndY&lt;T&gt;(T)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_FillX__1___0_">FluentExtensions.FillX&lt;T&gt;(T)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_FillY__1___0_">FluentExtensions.FillY&lt;T&gt;(T)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_Fill__1___0_">FluentExtensions.Fill&lt;T&gt;(T)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_Height__1___0_System_Double_">FluentExtensions.Height&lt;T&gt;(T, double)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_Initialize__1___0_System_Action___0__">FluentExtensions.Initialize&lt;T&gt;(T, Action&lt;T&gt;)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_ObserveBindingContext__2___0_System_Action___0___1_System_String__System_Boolean_">FluentExtensions.ObserveBindingContext&lt;T, TSource&gt;(T, Action&lt;T, TSource, string&gt;, bool)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_ObserveDeepNestedProperty__5___0_System_Func___1___2__System_String_System_Func___2___3__System_String_System_Func___3___4__System_String_System_Action___0___4____4_System_Boolean_">FluentExtensions.ObserveDeepNestedProperty&lt;T, TSource, TIntermediate1, TIntermediate2, TProperty&gt;(T, Func&lt;TSource, TIntermediate1&gt;, string, Func&lt;TIntermediate1, TIntermediate2&gt;, string, Func&lt;TIntermediate2, TProperty&gt;, string, Action&lt;T, TProperty&gt;, TProperty, bool)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_ObserveDeep__4___0_System_Func___1___2__System_String_System_Func___2___3__System_String_System_Action___0___3____3_System_Boolean_">FluentExtensions.ObserveDeep&lt;T, TSource, TIntermediate, TProperty&gt;(T, Func&lt;TSource, TIntermediate&gt;, string, Func&lt;TIntermediate, TProperty&gt;, string, Action&lt;T, TProperty&gt;, TProperty, bool)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_ObserveSelf__1___0_System_Action___0_System_String__">FluentExtensions.ObserveSelf&lt;T&gt;(T, Action&lt;T, string&gt;)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_ObserveTargetBindingContext__3___0___1_System_Action___0___1___2_System_String__System_Boolean_">FluentExtensions.ObserveTargetBindingContext&lt;T, TTarget, TSource&gt;(T, TTarget, Action&lt;T, TTarget, TSource, string&gt;, bool)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_ObserveTargetProperty__4___0_System_Linq_Expressions_Expression_System_Func___1___2___System_Linq_Expressions_Expression_System_Func___2___3___System_Action___0___3____3_System_Boolean_">FluentExtensions.ObserveTargetProperty&lt;T, TSource, TIntermediate, TProperty&gt;(T, Expression&lt;Func&lt;TSource, TIntermediate&gt;&gt;, Expression&lt;Func&lt;TIntermediate, TProperty&gt;&gt;, Action&lt;T, TProperty&gt;, TProperty, bool)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_Observe__2___0_System_Func___1__System_Action___0_System_String__System_String___">FluentExtensions.Observe&lt;T, TSource&gt;(T, Func&lt;TSource&gt;, Action&lt;T, string&gt;, string[])</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_Observe__2___0___1_System_Action___0_System_String__System_String___">FluentExtensions.Observe&lt;T, TSource&gt;(T, TSource, Action&lt;T, string&gt;, string[])</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_OnBindingContextSet__1___0_System_Action___0_System_Object__System_String___">FluentExtensions.OnBindingContextSet&lt;T&gt;(T, Action&lt;T, object&gt;, string[])</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_OnLongPressing__1___0_System_Action___0__">FluentExtensions.OnLongPressing&lt;T&gt;(T, Action&lt;T&gt;)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_OnPaint__1___0_System_Action___0_DrawnUi_Draw_DrawingContext__">FluentExtensions.OnPaint&lt;T&gt;(T, Action&lt;T, DrawingContext&gt;)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_OnTapped__1___0_System_Action___0__">FluentExtensions.OnTapped&lt;T&gt;(T, Action&lt;T&gt;)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_SetGrid__1___0_System_Int32_System_Int32_">FluentExtensions.SetGrid&lt;T&gt;(T, int, int)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_SetGrid__1___0_System_Int32_System_Int32_System_Int32_System_Int32_">FluentExtensions.SetGrid&lt;T&gt;(T, int, int, int, int)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_SetMargin__1___0_System_Double_">FluentExtensions.SetMargin&lt;T&gt;(T, double)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_SetMargin__1___0_System_Double_System_Double_">FluentExtensions.SetMargin&lt;T&gt;(T, double, double)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_SetMargin__1___0_System_Double_System_Double_System_Double_System_Double_">FluentExtensions.SetMargin&lt;T&gt;(T, double, double, double, double)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_SetWidth__1___0_System_Double_">FluentExtensions.SetWidth&lt;T&gt;(T, double)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_StartX__1___0_">FluentExtensions.StartX&lt;T&gt;(T)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_StartY__1___0_">FluentExtensions.StartY&lt;T&gt;(T)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithBackgroundColor__1___0_Microsoft_Maui_Graphics_Color_">FluentExtensions.WithBackgroundColor&lt;T&gt;(T, Color)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithCache__1___0_DrawnUi_Draw_SkiaCacheType_">FluentExtensions.WithCache&lt;T&gt;(T, SkiaCacheType)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithColumnSpan__1___0_System_Int32_">FluentExtensions.WithColumnSpan&lt;T&gt;(T, int)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithColumn__1___0_System_Int32_">FluentExtensions.WithColumn&lt;T&gt;(T, int)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithContent__1___0_DrawnUi_Draw_SkiaControl_">FluentExtensions.WithContent&lt;T&gt;(T, SkiaControl)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithHeightRequest__1___0_System_Double_">FluentExtensions.WithHeightRequest&lt;T&gt;(T, double)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithHorizontalOptions__1___0_Microsoft_Maui_Controls_LayoutOptions_">FluentExtensions.WithHorizontalOptions&lt;T&gt;(T, LayoutOptions)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithMargin__1___0_Microsoft_Maui_Thickness_">FluentExtensions.WithMargin&lt;T&gt;(T, Thickness)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithParent__1___0_DrawnUi_Draw_IDrawnBase_">FluentExtensions.WithParent&lt;T&gt;(T, IDrawnBase)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithRowSpan__1___0_System_Int32_">FluentExtensions.WithRowSpan&lt;T&gt;(T, int)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithRow__1___0_System_Int32_">FluentExtensions.WithRow&lt;T&gt;(T, int)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithTag__1___0_System_String_">FluentExtensions.WithTag&lt;T&gt;(T, string)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithVerticalOptions__1___0_Microsoft_Maui_Controls_LayoutOptions_">FluentExtensions.WithVerticalOptions&lt;T&gt;(T, LayoutOptions)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithWidthRequest__1___0_System_Double_">FluentExtensions.WithWidthRequest&lt;T&gt;(T, double)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.AnimateExtensions.html#DrawnUi_Draw_AnimateExtensions_AnimateWith_DrawnUi_Draw_SkiaControl_System_Func_DrawnUi_Draw_SkiaControl_System_Threading_Tasks_Task____">AnimateExtensions.AnimateWith(SkiaControl, params Func&lt;SkiaControl, Task&gt;[])</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.AnimateExtensions.html#DrawnUi_Draw_AnimateExtensions_FadeIn_DrawnUi_Draw_SkiaControl_System_Single_Microsoft_Maui_Easing_System_Threading_CancellationTokenSource_">AnimateExtensions.FadeIn(SkiaControl, float, Easing, CancellationTokenSource)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.AnimateExtensions.html#DrawnUi_Draw_AnimateExtensions_FadeOut_DrawnUi_Draw_SkiaControl_System_Single_Microsoft_Maui_Easing_System_Threading_CancellationTokenSource_">AnimateExtensions.FadeOut(SkiaControl, float, Easing, CancellationTokenSource)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.AnimateExtensions.html#DrawnUi_Draw_AnimateExtensions_Translate_DrawnUi_Draw_SkiaControl_System_Numerics_Vector2_System_Single_Microsoft_Maui_Easing_System_Threading_CancellationTokenSource_">AnimateExtensions.Translate(SkiaControl, Vector2, float, Easing, CancellationTokenSource)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.DrawnExtensions.html#DrawnUi_Draw_DrawnExtensions_AnimateRangeAsync_DrawnUi_Draw_SkiaControl_System_Action_System_Double__System_Double_System_Double_System_UInt32_Microsoft_Maui_Easing_System_Threading_CancellationTokenSource_">DrawnExtensions.AnimateRangeAsync(SkiaControl, Action&lt;double&gt;, double, double, uint, Easing, CancellationTokenSource)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_FindParentByType__1_DrawnUi_Draw_SkiaControl_">StaticResourcesExtensions.FindParentByType&lt;T&gt;(SkiaControl)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_FindParent__1_Microsoft_Maui_Controls_Element_">StaticResourcesExtensions.FindParent&lt;T&gt;(Element)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FindMauiContext_Microsoft_Maui_Controls_Element_System_Boolean_">InternalExtensions.FindMauiContext(Element, bool)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_GetParentsPath_Microsoft_Maui_Controls_Element_">InternalExtensions.GetParentsPath(Element)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_GetAllWithMyselfParents_Microsoft_Maui_Controls_VisualElement_">StaticResourcesExtensions.GetAllWithMyselfParents(VisualElement)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_DisposeControlAndChildren_Microsoft_Maui_IView_">InternalExtensions.DisposeControlAndChildren(IView)</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_">InternalExtensions.FromPlatform(object)</a>
  </div>

</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                  <li>
                    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_PlanesScroll.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.PlanesScroll%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A" class="contribution-link">Edit this page</a>
                  </li>
                  <li>
                    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Scroll/PlanesScroll.cs/#L10" class="contribution-link">View Source</a>
                  </li>
                </ul>
              </div>
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In this article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      
      <span>Generated by <strong>DocFX</strong></span>
          </div>
        </div>
      </footer>
    </div>

    <script type="text/javascript" src="../styles/docfx.vendor.min.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
