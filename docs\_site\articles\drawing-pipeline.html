<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
      <title>Understanding the Drawing Pipeline | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width">
      <meta name="title" content="Understanding the Drawing Pipeline | DrawnUi Documentation ">
    
    
      <link rel="shortcut icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../styles/docfx.vendor.min.css">
      <link rel="stylesheet" href="../styles/docfx.css">
      <link rel="stylesheet" href="../styles/main.css">
      <meta property="docfx:navrel" content="../toc.html">
      <meta property="docfx:tocrel" content="toc.html">
    
    <meta property="docfx:rel" content="../">
    
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first=First data-prev=Previous data-next=Next data-last=Last></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">

        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="">
<h1 id="understanding-the-drawing-pipeline" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="1">Understanding the Drawing Pipeline</h1>

<p sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="3">DrawnUI's drawing pipeline transforms your logical UI controls into pixel-perfect drawings on a Skia canvas.
This article explains how the pipeline works, from initial layout calculations to final rendering.<br>
DrawnUI is rather a rendering engine, not a UI-framework, and is not designed for an &quot;unaware&quot; usage.<br>
In order to use it effectively, one needs to understand how it works in order to achive the best performance and results.<br>
At the same time it's possible to create abstractional wrappers around performance-oriented controls, to automaticaly set caching types, layout options etc. that could be used without deep understanding of internals.</p>
<h2 id="overview" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="9">Overview</h2>
<p sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="11">The DrawnUI drawing pipeline consists of several key stages:</p>
<ol sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="13">
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="13"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="13">Executing pre-draw actions</strong> - including gestures processing, animations, etc.</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="14"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="14">Measuring and arranging</strong> - measure and arrange self and children if layout was invalidated</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="15"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="15">Drawing and caching</strong>   - painting and caching for future fast re-draws of recorded SKPicture or rasterized SKImage</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="16"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="16">Executing post-draw actions</strong> - like post-animations for overlays, etc.</li>
</ol>
<h2 id="pipeline-flow" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="18">Pipeline Flow</h2>
<h3 id="1-executing-pre-draw-actions" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="20">1. Executing Pre-Draw Actions</h3>
<p sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="22">Before any drawing occurs, DrawnUI executes several pre-draw actions including gestures processing, animations, etc.</p>
<h4 id="invalidation-triggers" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="24">Invalidation Triggers</h4>
<p sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="26">The pipeline begins when a control needs to be redrawn. This can happen due to:</p>
<ul sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="28">
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="28">Some controls property changed (color, size, text, etc.)</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="29">Layout changes (adding/removing children)</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="30">Animation updates</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="31">User interactions</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="32">Canvas received a &quot;redraw&quot; request from the engine (app went foreground, graphics context changed etc).</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="33">The top framework decided to re-draw our Canvas</li>
</ul>
<pre><code class="lang-csharp" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="35">// Most commonly used invalidation methods
control.Update();            // Mark for redraw (Update), invalidates cache
control.Repaint();           // Mark parent for redraw (Update), to repaint without destroying cache, at new positions, transformed etc
control.Invalidate();        // Invalidate and (maybe, depending on this control  logic) update
control.InvalidateMeasure(); // Just recalculate size and layout and update
control.Parent?.Invalidate() // When the above doesn't work if parent refuses to invalidate due to its internal logic
</code></pre>
<h4 id="pre-draw-operations" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="44">Pre-Draw Operations</h4>
<p sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="46"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="46">Gesture Processing:</strong></p>
<ul sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="47">
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="47">Process pending touch events and gestures</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="48">Update gesture states and animations</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="49">Handle user input through the control hierarchy</li>
</ul>
<p sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="51"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="51">Animation Updates:</strong></p>
<ul sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="52">
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="52">Execute frame-based animations</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="53">Update animated properties</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="54">Calculate interpolated values for smooth transitions</li>
</ul>
<p sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="56"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="56">Layout Validation:</strong></p>
<ul sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="57">
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="57">Check if measure/arrange is needed</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="58">Process layout invalidations</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="59">Prepare for drawing operations</li>
</ul>
<h3 id="2-measuring-and-arranging" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="61">2. Measuring and Arranging</h3>
<p sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="63">This stage handles the layout system - measure and arrange self and children if layout was invalidated.</p>
<h4 id="measure-stage" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="65">Measure Stage</h4>
<p sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="67">Controls calculate their desired size based on available space and content requirements.</p>
<pre><code class="lang-csharp" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="69">public virtual ScaledSize Measure(float widthConstraint, float heightConstraint, float scale)
{
    // Create measure request with constraints
    var request = CreateMeasureRequest(widthConstraint, heightConstraint, scale);

    // Measure content and return desired size
    return MeasureLayout(request, false);
}
</code></pre>
<p sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="80"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="80">Measure Process:</strong></p>
<ol sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="81">
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="81"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="81">Constraints Calculation</strong> - Determine available space considering margins and padding</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="82"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="82">Content Measurement</strong> - Measure child controls based on layout type</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="83"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="83">Size Request</strong> - Calculate final desired size</li>
</ol>
<p sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="85"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="85">Layout Types:</strong></p>
<ul sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="86">
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="86"><code sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="86">Absolute</code> - Children positioned at specific coordinates</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="87"><code sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="87">Column/Row</code> - Stack children vertically or horizontally</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="88"><code sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="88">Grid</code> - Arrange children in rows and columns</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="89"><code sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="89">Wrap</code> - Flow children with wrapping</li>
</ul>
<h4 id="arrange-stage" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="91">Arrange Stage</h4>
<p sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="93">The arrange stage positions controls within their allocated space and calculates final drawing rectangles.</p>
<pre><code class="lang-csharp" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="95">public virtual void Arrange(SKRect destination, float widthRequest, float heightRequest, float scale)
{
    // Pre-arrange validation
    if (!PreArrange(destination, widthRequest, heightRequest, scale))
        return;

    // Calculate final layout
    var layout = CalculateLayout(arrangingFor, widthRequest, heightRequest, scale);

    // Set drawing rectangle
    DrawingRect = layout;

    // Post-arrange processing
    PostArrange(destination, widthRequest, heightRequest, scale);
}
</code></pre>
<p sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="113"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="113">Arrange Process:</strong></p>
<ol sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="114">
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="114"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="114">Pre-Arrange</strong> - Validate and prepare for layout</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="115"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="115">Layout Calculation</strong> - Determine final position and size</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="116"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="116">Drawing Rectangle</strong> - Set the area where control will be drawn</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="117"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="117">Post-Arrange</strong> - Cache layout information and handle changes</li>
</ol>
<h3 id="3-drawing-and-caching" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="119">3. Drawing and Caching</h3>
<p sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="121">This is the core rendering stage - painting and caching for future fast re-draws of recorded SKPicture or rasterized SKImage.</p>
<h4 id="paint-stage" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="123">Paint Stage</h4>
<p sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="125">The paint stage renders the actual visual content to the Skia canvas.</p>
<pre><code class="lang-csharp" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="127">protected virtual void Paint(DrawingContext ctx)
{
    // Paint background
    PaintTintBackground(ctx.Context.Canvas, ctx.Destination);

    // Execute custom paint operations
    foreach (var action in ExecuteOnPaint.Values)
    {
        action?.Invoke(this, ctx);
    }
}
</code></pre>
<p sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="141"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="141">Drawing Context:</strong></p>
<ul sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="142">
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="142"><code sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="142">SKCanvas</code> - The Skia drawing surface</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="143"><code sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="143">SKRect Destination</code> - Where to draw in pixels</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="144"><code sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="144">float Scale</code> - Pixel density scaling factor</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="145"><code sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="145">object Parameters</code> - Optional custom parameters</li>
</ul>
<h4 id="caching-system" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="147">Caching System</h4>
<p sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="149">DrawnUI uses sophisticated caching to optimize rendering performance through render objects. This is crucial for achieving smooth 60fps animations and responsive UI.</p>
<h5 id="cache-types" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="151">Cache Types</h5>
<pre><code class="lang-csharp" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="153">public enum SkiaCacheType
{
    None,                    // No caching, direct drawing every frame
    Operations,              // Cache drawing operations as SKPicture  
    OperationsFull,          // Cache operations ignoring clipping bounds
    Image,                   // Cache as rasterized SKBitmap  
    ImageComposite,          // Advanced bitmap caching with composition
    ImageDoubleBuffered,     // Background thread rendering of cache of same same, while showing previous cache
    GPU                      // Hardware-accelerated GPU memory caching
}
</code></pre>
<p sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="166"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="166">Choosing the Right Cache Type:</strong></p>
<ul sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="167">
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="167"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="167">None</strong> - Do not cache scrolls, drawers etc, native views and their containers.</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="168"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="168">Operations</strong> - For anything, but maybe best for static vector content like text, icons, SVG.</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="169"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="169">Image</strong> - Rasterize anything once and then just draw the bitmap on every frame.</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="170"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="170">ImageDoubleBuffered</strong> - Perfect for recycled cells of same size</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="171"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="171">GPU</strong> - Use GPU memory for storing overlays, avoid large sizes.</li>
</ul>
<h5 id="render-object-pipeline" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="173">Render Object Pipeline</h5>
<pre><code class="lang-csharp" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="175">public virtual bool DrawUsingRenderObject(DrawingContext context,
    float widthRequest, float heightRequest)
{
    // 1. Arrange the control
    Arrange(context.Destination, widthRequest, heightRequest, context.Scale);

    // 2. Check if we can use cached render object
    if (RenderObject != null &amp;&amp; CheckCachedObjectValid(RenderObject))
    {
        DrawRenderObjectInternal(context, RenderObject);
        return true;
    }

    // 3. Create new render object if needed
    var cache = CreateRenderingObject(context, recordArea, oldObject, UsingCacheType,
        (ctx) =&gt; { PaintWithEffects(ctx); });

    // 4. Draw using the render object
    DrawRenderObjectInternal(context, cache);

    return true;
}
</code></pre>
<h5 id="cache-validation" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="200">Cache Validation</h5>
<p sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="202">Render objects are invalidated when:</p>
<ul sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="203">
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="203">Control size or position changes</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="204">Visual properties change (colors, effects, transforms)</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="205">Child controls are added, removed, or modified</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="206">Animation state updates require re-rendering</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="207">Hardware context changes (e.g., returning from background)</li>
</ul>
<h3 id="4-executing-post-draw-actions" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="209">4. Executing Post-Draw Actions</h3>
<p sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="211">After the main drawing is complete, DrawnUI executes post-draw operations like post-animations for overlays, etc.</p>
<p sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="213"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="213">Post-Animations:</strong></p>
<ul sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="214">
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="214">Overlay effects and animations</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="215">Particle systems and visual effects</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="216">UI feedback animations (ripples, highlights)</li>
</ul>
<p sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="218"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="218">Smart Resource Management:</strong></p>
<ul sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="219">
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="219">Frame-based disposal through DisposeManager</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="220">Update animation states</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="221">Prepare for next frame</li>
</ul>
<h4 id="disposemanager" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="223">DisposeManager</h4>
<p sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="225">The <strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="225">DisposeManager</strong> is a resource management system that prevents crashes disposing resources in the middle of the drawing and ensures smooth performance by disposing packs of objects at once at the end of the frame. It is concurrent usage safe</p>
<p sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="227"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="227">Why It's Needed:</strong>
In high-performance rendering, resources like SKBitmap, SKPicture, and render objects might still be referenced by background threads, GPU operations, or cached render objects even after they're &quot;logically&quot; no longer needed. Immediate disposal can cause crashes or visual glitches.</p>
<p sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="230"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="230">How to use:</strong></p>
<pre><code class="lang-csharp" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="231">//call this
control.DisposeObject(resource);
</code></pre>
<p sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="236"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="236">Practice:</strong></p>
<pre><code class="lang-csharp" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="237">// WUpdating a cached image
var oldBitmap = this.CachedBitmap;
this.CachedBitmap = newBitmap;

// Don't dispose immediately - let DisposeManager handle it
DisposeObject(oldBitmap); // Will be disposed safely after drawing few frames
</code></pre>
<p sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="246"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="246">Benefits:</strong></p>
<ul sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="247">
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="247"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="247">Crash Prevention</strong> - Resources are safely disposed after GPU/background operations complete</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="248"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="248">Performance</strong> - No blocking waits or expensive synchronization</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="249"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="249">Automatic</strong> - Works transparently without developer intervention</li>
</ul>
<h2 id="gesture-processing-integration" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="251">Gesture Processing Integration</h2>
<p sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="253">Gesture processing is integrated throughout the pipeline, primarily during pre-draw actions. Canvas asynchronously receives gesture events from the native platform and accumulates them to be passed through the control hierarchy at the start of a new frame. Gesture events are processed in the order they were received, to the concerned control's <code sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="253">ISkiaGestureListener.OnSkiaGestureEvent</code> implementation. This is a technical method that should not be used directly - it calls <code sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="253">SkiaControl.ProcessGestures</code> method that can be safely overridden.</p>
<h3 id="gesture-parameters" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="255">Gesture Parameters</h3>
<h4 id="skiagesturesparameters" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="257">SkiaGesturesParameters</h4>
<p sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="258">Contains the core gesture information:</p>
<pre><code class="lang-csharp" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="260">public class SkiaGesturesParameters
{
    public TouchActionResult Type { get; set; }        // Down, Up, Tapped, Panning, etc.
    public TouchActionEventArgs Event { get; set; }    // Touch details and coordinates
}
</code></pre>
<p sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="268"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="268">TouchActionResult Types:</strong></p>
<ul sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="269">
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="269"><code sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="269">Down</code> - Initial touch contact</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="270"><code sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="270">Up</code> - Touch release</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="271"><code sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="271">Tapped</code> - Quick tap gesture</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="272"><code sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="272">Panning</code> - Drag/swipe movement</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="273"><code sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="273">LongPressing</code> - Extended press</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="274"><code sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="274">Cancelled</code> - Gesture interrupted</li>
</ul>
<p sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="276"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="276">TouchActionEventArgs Properties:</strong></p>
<ul sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="277">
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="277"><code sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="277">Location</code> - Current touch position</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="278"><code sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="278">StartingLocation</code> - Initial touch position</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="279"><code sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="279">Id</code> - Unique touch identifier for multi-touch</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="280"><code sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="280">NumberOfTouches</code> - Count of simultaneous touches</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="281"><code sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="281">Distance</code> - Movement delta and velocity information</li>
</ul>
<h4 id="gestureeventprocessinginfo" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="283">GestureEventProcessingInfo</h4>
<p sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="284">Manages coordinate transformations and gesture ownership:</p>
<pre><code class="lang-csharp" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="286">public struct GestureEventProcessingInfo
{
    public SKPoint MappedLocation { get; set; }        // Touch location with transforms applied
    public SKPoint ChildOffset { get; set; }           // Coordinate offset for child controls
    public SKPoint ChildOffsetDirect { get; set; }     // Direct offset without cached transforms
    public ISkiaGestureListener AlreadyConsumed { get; set; } // Tracks gesture ownership
}
</code></pre>
<h3 id="hit-testing-system" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="296">Hit Testing System</h3>
<p sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="298">Hit testing determines which controls can receive touch input through a multi-stage process:</p>
<h4 id="primary-hit-testing" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="300">Primary Hit Testing</h4>
<pre><code class="lang-csharp" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="301">public virtual bool HitIsInside(float x, float y)
{
    var hitbox = HitBoxAuto;  // Gets transformed drawing rectangle
    return hitbox.ContainsInclusive(x, y);
}

public virtual SKRect HitBoxAuto
{
    get
    {
        var moved = ApplyTransforms(DrawingRect);  // Apply all transforms
        return moved;
    }
}
</code></pre>
<h4 id="transform-aware-hit-testing" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="318">Transform-Aware Hit Testing</h4>
<p sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="319">The system accounts for control transformations (rotation, scale, translation):</p>
<pre><code class="lang-csharp" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="321">public virtual bool IsGestureForChild(SkiaControlWithRect child, SKPoint point)
{
    if (child.Control != null &amp;&amp; !child.Control.InputTransparent &amp;&amp; child.Control.CanDraw)
    {
        var transformed = child.Control.ApplyTransforms(child.HitRect);
        return transformed.ContainsInclusive(point.X, point.Y);
    }
    return false;
}
</code></pre>
<h4 id="coordinate-transformation" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="333">Coordinate Transformation</h4>
<p sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="334">Touch coordinates are transformed through the control hierarchy:</p>
<pre><code class="lang-csharp" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="336">public SKPoint TransformPointToLocalSpace(SKPoint pointInParentSpace)
{
    // Apply inverse transformation to get point in local space
    if (RenderTransformMatrix != SKMatrix.Identity &amp;&amp;
        RenderTransformMatrix.TryInvert(out SKMatrix inverse))
    {
        return inverse.MapPoint(pointInParentSpace);
    }
    return pointInParentSpace;
}
</code></pre>
<h3 id="gesture-processing-flow" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="349">Gesture Processing Flow</h3>
<h4 id="canvas-level-processing" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="351">Canvas-Level Processing</h4>
<p sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="352">The Canvas manages the main gesture processing loop:</p>
<pre><code class="lang-csharp" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="354">protected virtual void ProcessGestures(SkiaGesturesParameters args)
{
    // Create initial processing info with touch location
    var adjust = new GestureEventProcessingInfo(
        args.Event.Location.ToSKPoint(),
        SKPoint.Empty,
        SKPoint.Empty,
        null);

    // First pass: Process controls that already had input
    foreach (var hadInput in HadInput.Values)
    {
        var consumed = hadInput.OnSkiaGestureEvent(args, adjust);
        if (consumed != null) break;
    }

    // Second pass: Hit test all gesture listeners
    foreach (var listener in GestureListeners.GetListeners())
    {
        if (listener.HitIsInside(args.Event.StartingLocation.X, args.Event.StartingLocation.Y))
        {
            var consumed = listener.OnSkiaGestureEvent(args, adjust);
            if (consumed != null) break;
        }
    }
}
</code></pre>
<h4 id="control-level-processing" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="383">Control-Level Processing</h4>
<p sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="384">Individual controls process gestures with coordinate transformation:</p>
<pre><code class="lang-csharp" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="386">public ISkiaGestureListener OnSkiaGestureEvent(SkiaGesturesParameters args,
    GestureEventProcessingInfo apply)
{
    // Apply inverse transforms if control has transformations
    if (HasTransform &amp;&amp; RenderTransformMatrix.TryInvert(out SKMatrix inverse))
    {
        apply = new GestureEventProcessingInfo(
            inverse.MapPoint(apply.MappedLocation),
            apply.ChildOffset,
            apply.ChildOffsetDirect,
            apply.AlreadyConsumed
        );
    }

    // Process the gesture
    var result = ProcessGestures(args, apply);
    return result; // Return consumer or null
}
</code></pre>
<h4 id="practical-usage-example" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="407">Practical Usage Example</h4>
<pre><code class="lang-csharp" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="408">public override ISkiaGestureListener ProcessGestures(SkiaGesturesParameters args,
    GestureEventProcessingInfo apply)
{
    // Get local coordinates
    var point = TranslateInputOffsetToPixels(args.Event.Location, apply.ChildOffset);

    switch (args.Type)
    {
        case TouchActionResult.Down:
            IsPressed = true;
            return this; // Consume the gesture

        case TouchActionResult.Up:
            if (IsPressed)
            {
                IsPressed = false;
                OnClicked();
                return this;
            }
            break;
    }

    return null; // Don't consume, pass to other controls
}
</code></pre>
<h3 id="key-concepts" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="435">Key Concepts</h3>
<p sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="437"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="437">Gesture Consumption:</strong></p>
<ul sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="438">
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="438">Return <code sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="438">this</code> to consume the gesture and prevent it from reaching other controls</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="439">Return <code sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="439">null</code> to allow the gesture to continue through the hierarchy</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="440"><code sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="440">BlockGesturesBelow</code> property can block all gestures from reaching lower controls</li>
</ul>
<p sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="442"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="442">Coordinate Spaces:</strong></p>
<ul sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="443">
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="443"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="443">Canvas Space</strong> - Root coordinate system</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="444"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="444">Parent Space</strong> - Coordinates relative to immediate parent</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="445"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="445">Local Space</strong> - Coordinates relative to the control itself</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="446"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="446">Transformed Space</strong> - Coordinates accounting for all applied transforms</li>
</ul>
<p sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="448"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="448">Multi-Touch Support:</strong></p>
<ul sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="449">
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="449">Each touch has a unique <code sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="449">Id</code> for tracking</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="450"><code sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="450">NumberOfTouches</code> indicates simultaneous touches</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="451">Controls can handle complex multi-finger gestures</li>
</ul>
<h2 id="performance-optimizations" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="453">Performance Optimizations</h2>
<p sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="455">Understanding these optimizations is crucial for building high-performance DrawnUI applications.</p>
<h3 id="1-smart-caching-strategy" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="457">1. Smart Caching Strategy</h3>
<p sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="459"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="459">Choose Cache Types Wisely:</strong></p>
<ul sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="460">
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="460"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="460">Static Content</strong> - Use <code sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="460">Operations</code> for text, icons, simple shapes</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="461"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="461">Complex Graphics</strong> - Use <code sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="461">Image</code> for content with effects, shadows, gradients</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="462"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="462">Animated Content</strong> - Use <code sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="462">ImageDoubleBuffered</code> for smooth 60fps animations</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="463"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="463">High-Performance</strong> - Use <code sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="463">GPU</code> caching when hardware acceleration is available</li>
</ul>
<p sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="465"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="465">Cache Invalidation Best Practices:</strong></p>
<ul sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="466">
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="466">Batch property changes to minimize cache invalidations</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="467">Use <code sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="467">Repaint()</code> instead of <code sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="467">Update()</code> when only position/transform changes</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="468">Avoid frequent size changes that invalidate image caches</li>
</ul>
<h3 id="2-layout-system-optimization" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="470">2. Layout System Optimization</h3>
<p sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="472"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="472">Efficient Invalidation:</strong></p>
<ul sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="473">
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="473"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="473">Layout Dirty Tracking</strong> - Only re-layout when absolutely necessary</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="474"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="474">Measure Caching</strong> - Reuse previous measurements when constraints haven't changed</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="475"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="475">Viewport Limiting</strong> - Only process and measure visible content</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="476"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="476">Hierarchical Updates</strong> - Invalidate only affected branches of the control tree</li>
</ul>
<p sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="478"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="478">Layout Performance Tips:</strong></p>
<ul sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="479">
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="479">Prefer <code sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="479">Absolute</code> layout for static positioning</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="480">Use <code sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="480">Column/Row</code> for simple stacking scenarios</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="481">Reserve <code sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="481">Grid</code> for complex layouts that truly need it</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="482">Minimize deep nesting of layout containers</li>
</ul>
<h3 id="3-drawing-pipeline-optimizations" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="484">3. Drawing Pipeline Optimizations</h3>
<p sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="486"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="486">Rendering Efficiency:</strong></p>
<ul sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="487">
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="487"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="487">Clipping Optimization</strong> - Skip drawing operations outside visible bounds</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="488"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="488">Transform Caching</strong> - Reuse transformation matrices across frames</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="489"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="489">Effect Batching</strong> - Group similar drawing operations to reduce state changes</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="490"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="490">Background Rendering</strong> - Use double-buffered caching for complex animations</li>
</ul>
<h2 id="common-patterns" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="492">Common Patterns</h2>
<h3 id="custom-control-drawing" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="494">Custom Control Drawing</h3>
<pre><code class="lang-csharp" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="496">public class MyCustomControl : SkiaControl
{
    protected override void Paint(DrawingContext ctx)
    {
        base.Paint(ctx); // Paint background
        
        var canvas = ctx.Context.Canvas;
        var rect = ctx.Destination;
        
        // Custom drawing code here
        using var paint = new SKPaint
        {
            Color = SKColors.Blue,
            IsAntialias = true
        };
        
        canvas.DrawCircle(rect.MidX, rect.MidY, 
            Math.Min(rect.Width, rect.Height) / 2, paint);
    }
}
</code></pre>
<h3 id="layout-container" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="519">Layout Container</h3>
<pre><code class="lang-csharp" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="521">public class MyLayout : SkiaLayout
{
    protected override ScaledSize MeasureAbsolute(SKRect rectForChildrenPixels, float scale)
    {
        // Measure all children
        foreach (var child in Views)
        {
            var childSize = MeasureChild(child, 
                rectForChildrenPixels.Width, 
                rectForChildrenPixels.Height, scale);
        }
        
        // Return total size needed
        return ScaledSize.FromPixels(totalWidth, totalHeight, scale);
    }
    
    protected override void ArrangeChildren(SKRect rectForChildrenPixels, float scale)
    {
        // Position each child
        foreach (var child in Views)
        {
            var childRect = CalculateChildPosition(child, rectForChildrenPixels);
            child.Arrange(childRect, child.SizeRequest.Width, child.SizeRequest.Height, scale);
        }
    }
}
</code></pre>
<h2 id="debugging-the-pipeline" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="550">Debugging the Pipeline</h2>
<h3 id="performance-monitoring" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="552">Performance Monitoring</h3>
<pre><code class="lang-csharp" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="554">// Enable performance tracking
Super.EnableRenderingStats = true;

// Monitor frame rates
var fps = canvasView.FPS;
var frameTime = canvasView.FrameTime;
</code></pre>
<h3 id="visual-debugging" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="563">Visual Debugging</h3>
<pre><code class="lang-csharp" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="565">// Show control boundaries
control.DebugShowBounds = true;

// Highlight invalidated areas
Super.ShowInvalidatedAreas = true;
</code></pre>
<h2 id="best-practices-for-performance" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="573">Best Practices for Performance</h2>
<ol sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="575">
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="575"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="575">Master Cache Types</strong> - Choose the right <code sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="575">SkiaCacheType</code> based on content characteristics</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="576"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="576">Understand Invalidation</strong> - Use the most appropriate invalidation method for each scenario</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="577"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="577">Optimize Paint Methods</strong> - Keep custom <code sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="577">Paint()</code> implementations lightweight and efficient</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="578"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="578">Profile Continuously</strong> - Use built-in performance monitoring to identify bottlenecks</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="579"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="579">Design for Caching</strong> - Structure your UI to take advantage of render object caching</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="580"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="580">Handle Gestures Smartly</strong> - Return appropriate consumers to optimize hit-testing performance</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="581"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="581">Batch Updates</strong> - Group property changes to minimize pipeline overhead</li>
</ol>
<h2 id="debugging-and-profiling" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="583">Debugging and Profiling</h2>
<pre><code class="lang-csharp" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="585">// Enable performance tracking
Super.EnableRenderingStats = true;

// Monitor frame rates and timing
var fps = canvasView.FPS;
var frameTime = canvasView.FrameTime;

// Visual debugging
control.DebugShowBounds = true;
Super.ShowInvalidatedAreas = true;
</code></pre>
<h2 id="conclusion" sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="598">Conclusion</h2>
<p sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="600">DrawnUI is a rendering engine that requires understanding its pipeline to achieve optimal results. Unlike traditional UI frameworks that hide rendering complexity, DrawnUI exposes these details to give you control over performance and visual quality.</p>
<p sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="602"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="602">Key Takeaways:</strong></p>
<ul sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="604">
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="604"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="604">Pipeline Awareness</strong> - Understanding the 4-stage pipeline helps you make informed decisions</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="605"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="605">Caching Strategy</strong> - Proper cache type selection is crucial for performance</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="606"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="606">Invalidation Control</strong> - Knowing when and how to invalidate prevents unnecessary work</li>
<li sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="607"><strong sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="607">Performance-First Design</strong> - Design your UI architecture with the pipeline in mind</li>
</ul>
<p sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="609">Understanding DrawnUI's internals enables applications that can achieve smooth 60fps animations, pixel-perfect custom controls, and responsive user experiences across all platforms.</p>
<p sourcefile="articles/drawing-pipeline.md" sourcestartlinenumber="611">Work with the pipeline design to create applications with smooth performance and visual quality.</p>

</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                  <li>
                    <a href="https://github.com/taublast/DrawnUi/blob/master/docs/articles/drawing-pipeline.md/#L1" class="contribution-link">Edit this page</a>
                  </li>
                </ul>
              </div>
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In this article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      
      <span>Generated by <strong>DocFX</strong></span>
          </div>
        </div>
      </footer>
    </div>

    <script type="text/javascript" src="../styles/docfx.vendor.min.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
