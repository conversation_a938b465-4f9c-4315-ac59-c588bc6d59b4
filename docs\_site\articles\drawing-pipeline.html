<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
      <title>Understanding the Drawing Pipeline | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width">
      <meta name="title" content="Understanding the Drawing Pipeline | DrawnUi Documentation ">
    
    
      <link rel="shortcut icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../styles/docfx.vendor.min.css">
      <link rel="stylesheet" href="../styles/docfx.css">
      <link rel="stylesheet" href="../styles/main.css">
      <meta property="docfx:navrel" content="../toc.html">
      <meta property="docfx:tocrel" content="toc.html">
    
    <meta property="docfx:rel" content="../">
    
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first=First data-prev=Previous data-next=Next data-last=Last></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">

        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="">
<h1 id="understanding-the-drawing-pipeline">Understanding the Drawing Pipeline</h1>

<p>DrawnUI's drawing pipeline is a sophisticated rendering system that transforms your UI controls into pixel-perfect graphics on a Skia canvas. This article explains how the pipeline works, from initial layout calculations to final pixel rendering.</p>
<h2 id="overview">Overview</h2>
<p>The DrawnUI drawing pipeline consists of several key stages:</p>
<ol>
<li><strong>Invalidation</strong> - Marking controls that need updates</li>
<li><strong>Measure</strong> - Calculating desired sizes for controls</li>
<li><strong>Arrange</strong> - Positioning controls within their containers</li>
<li><strong>Paint</strong> - Rendering controls to the canvas</li>
<li><strong>Caching</strong> - Optimizing performance through render object caching</li>
<li><strong>Gesture Processing</strong> - Handling user input and interactions</li>
</ol>
<h2 id="pipeline-flow">Pipeline Flow</h2>
<h3 id="1-invalidation-stage">1. Invalidation Stage</h3>
<p>The pipeline begins when a control needs to be redrawn. This can happen due to:</p>
<ul>
<li>Property changes (color, size, text, etc.)</li>
<li>Layout changes (adding/removing children)</li>
<li>Animation updates</li>
<li>User interactions</li>
</ul>
<pre><code class="lang-csharp">// Invalidation methods
control.InvalidateMeasure();  // Recalculate size and layout
control.Update();             // Mark for redraw
control.Repaint();           // Simple repaint without layout changes
</code></pre>
<p><strong>Key Classes:</strong></p>
<ul>
<li><code>SkiaControl.InvalidateMeasureInternal()</code> - Core invalidation logic</li>
<li><code>SkiaControl.InvalidateWithChildren()</code> - Propagates invalidation to children</li>
</ul>
<h3 id="2-measure-stage">2. Measure Stage</h3>
<p>During the measure stage, controls calculate their desired size based on available space and content requirements.</p>
<pre><code class="lang-csharp">public virtual ScaledSize Measure(float widthConstraint, float heightConstraint, float scale)
{
    // Create measure request with constraints
    var request = CreateMeasureRequest(widthConstraint, heightConstraint, scale);
    
    // Measure content and return desired size
    return MeasureLayout(request, false);
}
</code></pre>
<p><strong>Measure Process:</strong></p>
<ol>
<li><strong>Constraints Calculation</strong> - Determine available space considering margins and padding</li>
<li><strong>Content Measurement</strong> - Measure child controls based on layout type</li>
<li><strong>Size Request</strong> - Calculate final desired size</li>
</ol>
<p><strong>Layout Types:</strong></p>
<ul>
<li><code>Absolute</code> - Children positioned at specific coordinates</li>
<li><code>Column/Row</code> - Stack children vertically or horizontally</li>
<li><code>Grid</code> - Arrange children in rows and columns</li>
<li><code>Wrap</code> - Flow children with wrapping</li>
</ul>
<h3 id="3-arrange-stage">3. Arrange Stage</h3>
<p>The arrange stage positions controls within their allocated space and calculates final drawing rectangles.</p>
<pre><code class="lang-csharp">public virtual void Arrange(SKRect destination, float widthRequest, float heightRequest, float scale)
{
    // Pre-arrange validation
    if (!PreArrange(destination, widthRequest, heightRequest, scale))
        return;
        
    // Calculate final layout
    var layout = CalculateLayout(arrangingFor, widthRequest, heightRequest, scale);
    
    // Set drawing rectangle
    DrawingRect = layout;
    
    // Post-arrange processing
    PostArrange(destination, widthRequest, heightRequest, scale);
}
</code></pre>
<p><strong>Arrange Process:</strong></p>
<ol>
<li><strong>Pre-Arrange</strong> - Validate and prepare for layout</li>
<li><strong>Layout Calculation</strong> - Determine final position and size</li>
<li><strong>Drawing Rectangle</strong> - Set the area where control will be drawn</li>
<li><strong>Post-Arrange</strong> - Cache layout information and handle changes</li>
</ol>
<h3 id="4-paint-stage">4. Paint Stage</h3>
<p>The paint stage renders the actual visual content to the Skia canvas.</p>
<pre><code class="lang-csharp">protected virtual void Paint(DrawingContext ctx)
{
    // Paint background
    PaintTintBackground(ctx.Context.Canvas, ctx.Destination);
    
    // Execute custom paint operations
    foreach (var action in ExecuteOnPaint.Values)
    {
        action?.Invoke(this, ctx);
    }
}
</code></pre>
<p><strong>Drawing Context:</strong></p>
<ul>
<li><code>SKCanvas</code> - The Skia drawing surface</li>
<li><code>SKRect Destination</code> - Where to draw in pixels</li>
<li><code>float Scale</code> - Pixel density scaling factor</li>
<li><code>object Parameters</code> - Optional custom parameters</li>
</ul>
<h3 id="5-caching-system">5. Caching System</h3>
<p>DrawnUI uses sophisticated caching to optimize rendering performance through render objects.</p>
<h4 id="cache-types">Cache Types</h4>
<pre><code class="lang-csharp">public enum SkiaCacheType
{
    None,                    // No caching, direct drawing
    Operations,              // Cache drawing operations (SKPicture)
    OperationsFull,          // Cache operations ignoring clipping
    Image,                   // Cache as bitmap (SKBitmap)
    ImageComposite,          // Advanced bitmap caching
    ImageDoubleBuffered,     // Background thread caching
    GPU                      // Hardware-accelerated caching
}
</code></pre>
<h4 id="render-object-pipeline">Render Object Pipeline</h4>
<pre><code class="lang-csharp">public virtual bool DrawUsingRenderObject(DrawingContext context, 
    float widthRequest, float heightRequest)
{
    // 1. Arrange the control
    Arrange(context.Destination, widthRequest, heightRequest, context.Scale);
    
    // 2. Check if we can use cached render object
    if (RenderObject != null &amp;&amp; CheckCachedObjectValid(RenderObject))
    {
        DrawRenderObjectInternal(context, RenderObject);
        return true;
    }
    
    // 3. Create new render object if needed
    var cache = CreateRenderingObject(context, recordArea, oldObject, UsingCacheType,
        (ctx) =&gt; { PaintWithEffects(ctx); });
        
    // 4. Draw using the render object
    DrawRenderObjectInternal(context, cache);
    
    return true;
}
</code></pre>
<h4 id="cache-validation">Cache Validation</h4>
<p>Render objects are invalidated when:</p>
<ul>
<li>Control size changes</li>
<li>Visual properties change (colors, effects, etc.)</li>
<li>Child controls are modified</li>
<li>Animation state updates</li>
</ul>
<h3 id="6-gesture-processing">6. Gesture Processing</h3>
<p>The gesture system processes user input through a hierarchical hit-testing approach.</p>
<pre><code class="lang-csharp">public ISkiaGestureListener OnSkiaGestureEvent(SkiaGesturesParameters args, 
    GestureEventProcessingInfo apply)
{
    // Process gesture if control can handle input
    if (CanDraw &amp;&amp; this is ISkiaGestureListener listener)
    {
        var result = ProcessGestures(args, apply);
        return result; // Return consumer or null
    }
    return null;
}
</code></pre>
<p><strong>Gesture Flow:</strong></p>
<ol>
<li><strong>Hit Testing</strong> - Determine which controls are under the touch point</li>
<li><strong>Gesture Recognition</strong> - Identify gesture type (tap, drag, pinch, etc.)</li>
<li><strong>Event Propagation</strong> - Pass gestures through control hierarchy</li>
<li><strong>Consumption</strong> - Allow controls to consume or pass through gestures</li>
</ol>
<h2 id="performance-optimizations">Performance Optimizations</h2>
<h3 id="1-render-object-caching">1. Render Object Caching</h3>
<ul>
<li><strong>Operations Caching</strong> - Best for static content like text and shapes</li>
<li><strong>Image Caching</strong> - Ideal for complex graphics with effects</li>
<li><strong>Double Buffering</strong> - Renders on background thread for smooth animations</li>
</ul>
<h3 id="2-layout-optimization">2. Layout Optimization</h3>
<ul>
<li><strong>Layout Dirty Tracking</strong> - Only re-layout when necessary</li>
<li><strong>Measure Caching</strong> - Reuse previous measurements when possible</li>
<li><strong>Viewport Limiting</strong> - Only process visible content</li>
</ul>
<h3 id="3-drawing-optimizations">3. Drawing Optimizations</h3>
<ul>
<li><strong>Clipping</strong> - Skip drawing outside visible areas</li>
<li><strong>Transform Caching</strong> - Reuse transformation matrices</li>
<li><strong>Effect Batching</strong> - Group similar drawing operations</li>
</ul>
<h2 id="common-patterns">Common Patterns</h2>
<h3 id="custom-control-drawing">Custom Control Drawing</h3>
<pre><code class="lang-csharp">public class MyCustomControl : SkiaControl
{
    protected override void Paint(DrawingContext ctx)
    {
        base.Paint(ctx); // Paint background
        
        var canvas = ctx.Context.Canvas;
        var rect = ctx.Destination;
        
        // Custom drawing code here
        using var paint = new SKPaint
        {
            Color = SKColors.Blue,
            IsAntialias = true
        };
        
        canvas.DrawCircle(rect.MidX, rect.MidY, 
            Math.Min(rect.Width, rect.Height) / 2, paint);
    }
}
</code></pre>
<h3 id="layout-container">Layout Container</h3>
<pre><code class="lang-csharp">public class MyLayout : SkiaLayout
{
    protected override ScaledSize MeasureAbsolute(SKRect rectForChildrenPixels, float scale)
    {
        // Measure all children
        foreach (var child in Views)
        {
            var childSize = MeasureChild(child, 
                rectForChildrenPixels.Width, 
                rectForChildrenPixels.Height, scale);
        }
        
        // Return total size needed
        return ScaledSize.FromPixels(totalWidth, totalHeight, scale);
    }
    
    protected override void ArrangeChildren(SKRect rectForChildrenPixels, float scale)
    {
        // Position each child
        foreach (var child in Views)
        {
            var childRect = CalculateChildPosition(child, rectForChildrenPixels);
            child.Arrange(childRect, child.SizeRequest.Width, child.SizeRequest.Height, scale);
        }
    }
}
</code></pre>
<h2 id="debugging-the-pipeline">Debugging the Pipeline</h2>
<h3 id="performance-monitoring">Performance Monitoring</h3>
<pre><code class="lang-csharp">// Enable performance tracking
Super.EnableRenderingStats = true;

// Monitor frame rates
var fps = canvasView.FPS;
var frameTime = canvasView.FrameTime;
</code></pre>
<h3 id="visual-debugging">Visual Debugging</h3>
<pre><code class="lang-csharp">// Show control boundaries
control.DebugShowBounds = true;

// Highlight invalidated areas
Super.ShowInvalidatedAreas = true;
</code></pre>
<h2 id="best-practices">Best Practices</h2>
<ol>
<li><strong>Use Appropriate Caching</strong> - Choose the right <code>SkiaCacheType</code> for your content</li>
<li><strong>Minimize Invalidations</strong> - Batch property changes to reduce redraws</li>
<li><strong>Optimize Custom Paint</strong> - Keep <code>Paint()</code> methods lightweight</li>
<li><strong>Profile Performance</strong> - Use built-in performance monitoring tools</li>
<li><strong>Handle Gestures Efficiently</strong> - Return appropriate consumers to optimize hit-testing</li>
</ol>
<h2 id="conclusion">Conclusion</h2>
<p>Understanding DrawnUI's drawing pipeline helps you build efficient, responsive applications. The pipeline's sophisticated caching and optimization systems ensure smooth performance while providing the flexibility to create custom, pixel-perfect user interfaces.</p>
<p>The key to success with DrawnUI is working with the pipeline rather than against it - use the provided caching mechanisms, minimize unnecessary invalidations, and leverage the hierarchical layout system for optimal performance.</p>

</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                  <li>
                    <a href="https://github.com/taublast/DrawnUi/blob/master/docs/articles/drawing-pipeline.md/#L1" class="contribution-link">Edit this page</a>
                  </li>
                </ul>
              </div>
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In this article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      
      <span>Generated by <strong>DocFX</strong></span>
          </div>
        </div>
      </footer>
    </div>

    <script type="text/javascript" src="../styles/docfx.vendor.min.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>
